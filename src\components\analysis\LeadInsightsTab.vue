<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { Database } from '../../types/supabase';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const isVisible = ref(false);

onMounted(() => {
  // Trigger animation after component mounts
  setTimeout(() => {
    isVisible.value = true;
  }, 100);
});

// Generate lead qualification questions from AI analysis or fallback
const leadQualificationQuestions = computed(() => {
  const lighthouseData = props.analysis.lighthouse_data as any;

  // Use AI-generated questions if available
  if (lighthouseData?.leadInsights?.qualificationQuestions) {
    return lighthouseData.leadInsights.qualificationQuestions.map((question: string, index: number) => ({
      category: index < 2 ? 'Business Context' : index < 4 ? 'Target Audience' : 'Optimization',
      question: question,
      purpose: 'AI-generated based on website analysis',
      followUp: ['Specific details', 'Current metrics', 'Goals', 'Challenges']
    }));
  }

  // Fallback questions based on analysis findings
  const questions = [];

  // Base questions for all analyses
  questions.push({
    category: 'Business Context',
    question: 'What is your primary business goal for this landing page?',
    purpose: 'Understanding business objectives helps prioritize optimization efforts',
    followUp: ['Lead generation', 'Sales conversion', 'Brand awareness', 'Product education']
  });

  questions.push({
    category: 'Target Audience',
    question: 'Who is your ideal customer visiting this page?',
    purpose: 'Audience insights help tailor messaging and design improvements',
    followUp: ['Demographics', 'Pain points', 'Buying behavior', 'Technical expertise level']
  });

  // Performance-based questions
  if (props.analysis.performance_score && props.analysis.performance_score < 70) {
    questions.push({
      category: 'Technical Constraints',
      question: 'What technical limitations might affect page performance improvements?',
      purpose: 'Understanding constraints helps create realistic optimization roadmaps',
      followUp: ['Development resources', 'Platform limitations', 'Budget constraints', 'Timeline requirements']
    });
  }

  // SEO-based questions
  if (props.analysis.seo_score && props.analysis.seo_score < 80) {
    questions.push({
      category: 'Content Strategy',
      question: 'What keywords and topics are most important for your business?',
      purpose: 'SEO improvements should align with business-critical search terms',
      followUp: ['Primary keywords', 'Content calendar', 'Competitor analysis', 'Search volume priorities']
    });
  }

  // Conversion-focused questions
  questions.push({
    category: 'Conversion Goals',
    question: 'What specific action do you want visitors to take on this page?',
    purpose: 'Clear conversion goals help optimize the user journey and CTA placement',
    followUp: ['Primary CTA', 'Secondary actions', 'Form completion', 'Contact preferences']
  });

  return questions;
});

// Get lead insights from AI-generated data
const leadInsights = computed(() => {
  const insights = [];

  // Try to get lead insights from lighthouse_data (where comprehensive analysis is stored)
  const lighthouseData = props.analysis.lighthouse_data as any;
  if (lighthouseData?.leadInsights) {
    return lighthouseData.leadInsights.roadmapSuggestions?.map((suggestion: any, index: number) => ({
      type: index % 2 === 0 ? 'opportunity' : 'insight',
      title: suggestion.title,
      description: suggestion.description,
      impact: 'High',
      questions: lighthouseData.leadInsights.qualificationQuestions?.slice(index * 2, (index * 2) + 2) || [],
      businessValue: suggestion.expectedImpact
    })) || [];
  }

  // Fallback: Generate insights based on analysis data
  if (props.analysis.performance_score && props.analysis.performance_score < 70) {
    insights.push({
      type: 'opportunity',
      title: 'Performance Optimization Opportunity',
      description: 'Poor page performance is likely causing visitor drop-off. This represents a significant lead generation opportunity.',
      impact: 'High',
      questions: ['How many visitors do you lose due to slow loading?', 'What is your current bounce rate?'],
      businessValue: 'Improving performance could increase lead capture by 15-25%'
    });
  }

  // SEO insights
  if (props.analysis.seo_score) {
    if (props.analysis.seo_score < 80) {
      insights.push({
        type: 'growth',
        title: 'SEO Lead Generation Potential',
        description: 'SEO improvements could significantly increase organic traffic and lead volume.',
        impact: 'Medium',
        questions: ['What is your current organic traffic volume?', 'Which keywords drive the most qualified leads?'],
        businessValue: 'Better SEO could increase qualified leads by 20-40%'
      });
    }
  }

  // Conversion insights
  insights.push({
    type: 'conversion',
    title: 'Conversion Rate Optimization',
    description: 'Strategic improvements to messaging and design could significantly boost lead conversion rates.',
    impact: 'High',
    questions: ['What is your current conversion rate?', 'What objections do prospects typically have?'],
    businessValue: 'CRO improvements typically increase conversions by 10-30%'
  });

  // Content insights
  insights.push({
    type: 'content',
    title: 'Content-Driven Lead Nurturing',
    description: 'Enhanced content strategy could improve lead quality and nurturing effectiveness.',
    impact: 'Medium',
    questions: ['How do you currently nurture leads?', 'What content performs best for your audience?'],
    businessValue: 'Better content can improve lead quality scores by 25-50%'
  });

  return insights;
});

// AI-generated roadmap suggestions
const roadmapSuggestions = computed(() => {
  const lighthouseData = props.analysis.lighthouse_data as any;

  // Use AI-generated roadmap if available
  if (lighthouseData?.leadInsights?.roadmapSuggestions) {
    return lighthouseData.leadInsights.roadmapSuggestions;
  }

  // Fallback roadmap based on analysis findings
  const roadmap = [];

  // Immediate improvements (0-2 weeks)
  roadmap.push({
    phase: 'Immediate (0-2 weeks)',
    title: 'Quick Performance Wins',
    description: 'Optimize images, enable compression, and fix critical loading issues',
    timeline: '1-2 weeks',
    expectedImpact: 'Reduce bounce rate by 10-15%'
  });

  // Short-term improvements (1-3 months)
  if (props.analysis.seo_score && props.analysis.seo_score < 80) {
    roadmap.push({
      phase: 'Short-term (1-3 months)',
      title: 'SEO Foundation Enhancement',
      description: 'Improve meta tags, content structure, and technical SEO elements',
      timeline: '4-8 weeks',
      expectedImpact: 'Increase organic traffic by 20-30%'
    });
  }

  // Long-term strategic improvements (3-6 months)
  roadmap.push({
    phase: 'Long-term (3-6 months)',
    title: 'Conversion Optimization Program',
    description: 'A/B test key elements, implement advanced analytics, and optimize user journey',
    timeline: '3-6 months',
    expectedImpact: 'Improve conversion rate by 25-40%'
  });

  return roadmap;
});

const getInsightIcon = (type: string) => {
  switch (type) {
    case 'opportunity':
      return 'M13 10V3L4 14h7v7l9-11h-7z';
    case 'growth':
      return 'M7 14l9-9 9 9M5 21h14';
    case 'conversion':
      return 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z';
    case 'content':
      return 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
    default:
      return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
  }
};

const getInsightColor = (type: string) => {
  switch (type) {
    case 'opportunity':
      return 'bg-blue-50 border-blue-200 text-blue-800';
    case 'growth':
      return 'bg-green-50 border-green-200 text-green-800';
    case 'conversion':
      return 'bg-purple-50 border-purple-200 text-purple-800';
    case 'content':
      return 'bg-orange-50 border-orange-200 text-orange-800';
    default:
      return 'bg-gray-50 border-gray-200 text-gray-800';
  }
};
</script>

<template>
  <div 
    class="transition-all duration-700 ease-out transform"
    :class="isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'"
  >
    <div class="space-y-8">
      <!-- Lead Qualification Questions -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center mb-6">
          <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="text-lg font-semibold text-gray-900">Lead Qualification Questions</h3>
        </div>
        
        <p class="text-gray-600 mb-6">
          Use these strategic questions to better understand your prospects and qualify leads based on your analysis results.
        </p>

        <div class="space-y-6">
          <div 
            v-for="(question, index) in leadQualificationQuestions" 
            :key="index"
            class="border border-gray-200 rounded-lg p-4 transition-all duration-300 ease-out transform"
            :class="isVisible ? 'translate-x-0 opacity-100' : '-translate-x-4 opacity-0'"
            :style="{ transitionDelay: `${index * 150 + 300}ms` }"
          >
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-2">
                  {{ question.category }}
                </span>
                <h4 class="font-medium text-gray-900 mb-2">{{ question.question }}</h4>
                <p class="text-sm text-gray-600 mb-3">{{ question.purpose }}</p>
              </div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-3">
              <p class="text-xs font-medium text-gray-700 mb-2">Follow-up areas to explore:</p>
              <div class="flex flex-wrap gap-2">
                <span 
                  v-for="followUp in question.followUp" 
                  :key="followUp"
                  class="inline-flex items-center px-2 py-1 rounded text-xs bg-white text-gray-600 border border-gray-200"
                >
                  {{ followUp }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Lead Insights -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center mb-6">
          <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <h3 class="text-lg font-semibold text-gray-900">Lead Generation Insights</h3>
        </div>
        
        <p class="text-gray-600 mb-6">
          Strategic insights to help you understand the lead generation potential and business impact of optimization efforts.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div 
            v-for="(insight, index) in leadInsights" 
            :key="index"
            class="border rounded-lg p-4 transition-all duration-300 ease-out transform"
            :class="[
              getInsightColor(insight.type),
              isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
            ]"
            :style="{ transitionDelay: `${index * 200 + 600}ms` }"
          >
            <div class="flex items-start mb-3">
              <svg class="w-5 h-5 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getInsightIcon(insight.type)" />
              </svg>
              <div class="flex-1">
                <h4 class="font-medium mb-1">{{ insight.title }}</h4>
                <p class="text-sm mb-3">{{ insight.description }}</p>
                
                <div class="mb-3">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white bg-opacity-60">
                    {{ insight.impact }} Impact
                  </span>
                </div>
                
                <div class="bg-white bg-opacity-60 rounded p-3 mb-3">
                  <p class="text-xs font-medium mb-2">Key questions to ask:</p>
                  <ul class="text-xs space-y-1">
                    <li v-for="question in insight.questions" :key="question" class="flex items-start">
                      <span class="w-1 h-1 bg-current rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {{ question }}
                    </li>
                  </ul>
                </div>
                
                <div class="bg-white bg-opacity-80 rounded p-2">
                  <p class="text-xs font-medium text-green-700">💡 {{ insight.businessValue }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Items -->
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
        <div class="flex items-center mb-4">
          <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
          <h3 class="text-lg font-semibold text-blue-900">Next Steps for Lead Generation</h3>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-white rounded-lg p-4 border border-blue-200">
            <h4 class="font-medium text-blue-900 mb-2">1. Qualify Prospects</h4>
            <p class="text-sm text-blue-700">Use the qualification questions to better understand prospect needs and pain points.</p>
          </div>
          <div class="bg-white rounded-lg p-4 border border-blue-200">
            <h4 class="font-medium text-blue-900 mb-2">2. Prioritize Improvements</h4>
            <p class="text-sm text-blue-700">Focus on high-impact optimizations that align with your lead generation goals.</p>
          </div>
          <div class="bg-white rounded-lg p-4 border border-blue-200">
            <h4 class="font-medium text-blue-900 mb-2">3. Track & Measure</h4>
            <p class="text-sm text-blue-700">Monitor lead quality and conversion improvements after implementing changes.</p>
          </div>
        </div>
      </div>

      <!-- AI-Generated Strategic Roadmap -->
      <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 p-6">
        <div class="flex items-center mb-6">
          <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h3 class="text-lg font-semibold text-green-900">Strategic Lead Generation Roadmap</h3>
        </div>
        <p class="text-green-700 mb-6">
          AI-generated implementation plan to systematically improve your lead generation performance.
        </p>

        <div class="space-y-4">
          <div
            v-for="(item, index) in roadmapSuggestions"
            :key="index"
            class="bg-white rounded-lg p-4 border border-green-200 transition-all duration-300 ease-out transform"
            :class="isVisible ? 'translate-x-0 opacity-100' : '-translate-x-4 opacity-0'"
            :style="{ transitionDelay: `${index * 200 + 800}ms` }"
          >
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1">
                <div class="flex items-center mb-2">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-3">
                    {{ item.phase }}
                  </span>
                  <span class="text-xs text-gray-500">{{ item.timeline }}</span>
                </div>
                <h4 class="font-medium text-gray-900 mb-2">{{ item.title }}</h4>
                <p class="text-sm text-gray-600 mb-3">{{ item.description }}</p>
                <div class="bg-green-50 rounded p-2">
                  <p class="text-xs font-medium text-green-700">📈 Expected Impact: {{ item.expectedImpact }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-6 p-4 bg-green-100 rounded-lg">
          <p class="text-sm text-green-800">
            <strong>💡 Pro Tip:</strong> Implement these phases sequentially for maximum impact. Each phase builds upon the previous one to create compound improvements in lead generation.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Additional animation styles if needed */
</style>
