---
import DashboardLayout from '../../layouts/DashboardLayout.astro';
import ProfileForm from '../../components/profile/ProfileForm.vue';
import ExportDataButton from '../../components/profile/ExportDataButton.vue';
---

<DashboardLayout title="Your Profile - ConvertIQ">
  <div class="max-w-3xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">Your Profile</h1>
    
    <div class="bg-white rounded-lg shadow p-6 mb-8">
      <ProfileForm client:load />
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-xl font-medium mb-4">Data Management</h2>
      <p class="text-gray-600 mb-4">Export all your data or manage your account settings below.</p>
      
      <div class="flex flex-wrap gap-4">
        <ExportDataButton client:load />
        
        <button class="px-4 py-2 bg-error-500 text-white rounded hover:bg-red-700 transition-colors">
          Delete Account
        </button>
      </div>
    </div>
  </div>
</DashboardLayout>