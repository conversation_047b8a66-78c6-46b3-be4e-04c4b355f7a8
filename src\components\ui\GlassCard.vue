<script setup lang="ts">
interface Props {
  className?: string;
  hover?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  className: '',
  hover: true
});
</script>

<template>
  <div
    :class="[
      'backdrop-blur-xl bg-white/70 dark:bg-gray-900/70 border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-xl shadow-gray-900/5 dark:shadow-black/20',
      hover && 'hover:bg-white/80 dark:hover:bg-gray-900/80 hover:shadow-2xl hover:shadow-gray-900/10 dark:hover:shadow-black/30 hover:-translate-y-1',
      'transition-all duration-300 ease-out',
      className
    ]"
  >
    <slot />
  </div>
</template>