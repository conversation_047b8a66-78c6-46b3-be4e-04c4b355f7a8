<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { Database } from '../../types/supabase';
import { CORE_WEB_VITALS_THRESHOLDS, evaluateMetric } from '../../lib/performance-analyzer';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const showAdvancedMetrics = ref(false);

const performanceGradeColor = computed(() => {
  switch (props.analysis.performance_grade) {
    case 'A': return 'bg-green-500 text-white';
    case 'B': return 'bg-blue-500 text-white';
    case 'C': return 'bg-yellow-500 text-white';
    case 'D': return 'bg-orange-500 text-white';
    case 'F': return 'bg-red-500 text-white';
    default: return 'bg-gray-500 text-white';
  }
});

const conversionImpactMessage = computed(() => {
  const score = props.analysis.performance_score || 0;
  if (score >= 90) return 'Excellent performance with potential for 2-5% conversion improvement';
  if (score >= 80) return 'Good performance with potential for 5-15% conversion improvement';
  if (score >= 70) return 'Fair performance with potential for 15-25% conversion improvement';
  if (score >= 60) return 'Poor performance with potential for 25-40% conversion improvement';
  return 'Critical performance issues with potential for 40%+ conversion improvement';
});

const coreWebVitals = computed(() => [
  {
    name: 'Largest Contentful Paint',
    value: props.analysis.lcp_score || 0,
    unit: 's',
    description: 'Time until largest element is rendered',
    status: evaluateMetric(props.analysis.lcp_score || 0, CORE_WEB_VITALS_THRESHOLDS.lcp)
  },
  {
    name: 'First Input Delay',
    value: props.analysis.fid_score || 0,
    unit: 'ms',
    description: 'Time to respond to user interaction',
    status: evaluateMetric(props.analysis.fid_score || 0, CORE_WEB_VITALS_THRESHOLDS.fid)
  },
  {
    name: 'Cumulative Layout Shift',
    value: props.analysis.cls_score || 0,
    unit: '',
    description: 'Visual stability during page load',
    status: evaluateMetric(props.analysis.cls_score || 0, CORE_WEB_VITALS_THRESHOLDS.cls)
  }
]);

const getStatusColor = (status: string) => {
  switch (status) {
    case 'good': return 'text-green-600';
    case 'needs-improvement': return 'text-yellow-600';
    case 'poor': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

const formatValue = (value: number, unit: string) => {
  if (unit === 's') return `${value.toFixed(2)}s`;
  if (unit === 'ms') return `${Math.round(value)}ms`;
  return value.toFixed(3);
};

const toggleAdvancedMetrics = () => {
  showAdvancedMetrics.value = !showAdvancedMetrics.value;
};
</script>

<template>
  <div class="space-y-6">
    <!-- Performance Overview -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center mb-4">
        <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
        <h3 class="text-lg font-semibold text-gray-900">Performance Overview</h3>
      </div>
      <p class="text-gray-600 mb-6">Core Web Vitals and performance metrics analysis</p>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Overall Performance Score -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500 mb-1">Overall Performance Score</p>
              <div class="flex items-center space-x-3">
                <span class="text-3xl font-bold text-orange-500">
                  {{ analysis.performance_score || 0 }}
                </span>
                <span class="text-gray-500">/100</span>
                <span 
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                  :class="performanceGradeColor"
                >
                  Grade {{ analysis.performance_grade || 'N/A' }}
                </span>
              </div>
            </div>
          </div>

          <!-- Conversion Impact -->
          <div class="bg-blue-50 rounded-lg p-4">
            <div class="flex items-start">
              <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              <div>
                <h4 class="font-medium text-blue-900 mb-1">Conversion Impact</h4>
                <p class="text-sm text-blue-700">{{ conversionImpactMessage }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Core Web Vitals -->
        <div class="space-y-4">
          <div 
            v-for="vital in coreWebVitals" 
            :key="vital.name"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex-1">
              <div class="flex items-center justify-between mb-1">
                <h5 class="font-medium text-gray-900 text-sm">{{ vital.name }}</h5>
                <span 
                  class="text-lg font-bold"
                  :class="getStatusColor(vital.status)"
                >
                  {{ formatValue(vital.value, vital.unit) }}
                </span>
              </div>
              <p class="text-xs text-gray-500">{{ vital.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Advanced Metrics Toggle -->
      <div class="mt-6 pt-6 border-t border-gray-200">
        <button
          @click="toggleAdvancedMetrics"
          class="flex items-center justify-between w-full text-left"
        >
          <span class="font-medium text-gray-900">Advanced Metrics</span>
          <svg 
            class="w-5 h-5 text-gray-400 transition-transform duration-300"
            :class="{ 'rotate-180': showAdvancedMetrics }"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        <!-- Advanced Metrics Content -->
        <div 
          v-if="showAdvancedMetrics"
          class="mt-4 p-4 bg-gray-50 rounded-lg transition-all duration-300 ease-in-out"
        >
          <div v-if="analysis.lighthouse_data" class="space-y-3">
            <h4 class="font-medium text-gray-900 mb-3">Full Lighthouse Report</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div class="text-center p-3 bg-white rounded border">
                <div class="font-semibold text-gray-900">Performance</div>
                <div class="text-lg font-bold text-orange-500">
                  {{ Math.round((analysis.lighthouse_data as any)?.categories?.performance?.score * 100) || 0 }}
                </div>
              </div>
              <div class="text-center p-3 bg-white rounded border">
                <div class="font-semibold text-gray-900">Accessibility</div>
                <div class="text-lg font-bold text-green-500">
                  {{ Math.round((analysis.lighthouse_data as any)?.categories?.accessibility?.score * 100) || 0 }}
                </div>
              </div>
              <div class="text-center p-3 bg-white rounded border">
                <div class="font-semibold text-gray-900">Best Practices</div>
                <div class="text-lg font-bold text-blue-500">
                  {{ Math.round((analysis.lighthouse_data as any)?.categories?.['best-practices']?.score * 100) || 0 }}
                </div>
              </div>
              <div class="text-center p-3 bg-white rounded border">
                <div class="font-semibold text-gray-900">SEO</div>
                <div class="text-lg font-bold text-purple-500">
                  {{ Math.round((analysis.lighthouse_data as any)?.categories?.seo?.score * 100) || 0 }}
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8 text-gray-500">
            <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p>Advanced metrics not available</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.rotate-180 {
  transform: rotate(180deg);
}
</style>
