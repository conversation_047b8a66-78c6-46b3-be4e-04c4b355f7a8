<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { supabase } from '../../lib/supabase';
import { chatWithAI } from '../../lib/openrouter';
import type { AnalysisWithMetadata, Message } from '../../types/analysis';

const props = defineProps<{
  analysisId: string;
}>();

const analysis = ref<AnalysisWithMetadata | null>(null);
const messages = ref<Message[]>([]);
const newMessage = ref('');
const loading = ref(true);
const sending = ref(false);
const error = ref('');
const messageEnd = ref<HTMLElement | null>(null);

onMounted(async () => {
  await Promise.all([
    loadAnalysis(),
    loadMessages()
  ]);
  
  scrollToBottom();
});

watch(() => messages.value.length, () => {
  scrollToBottom();
});

const loadAnalysis = async () => {
  try {
    const { data, error: fetchError } = await supabase
      .from('analyses')
      .select('*')
      .eq('id', props.analysisId)
      .single();
    
    if (fetchError) throw fetchError;
    
    analysis.value = data;
  } catch (e) {
    console.error('Error loading analysis:', e);
    error.value = 'Failed to load analysis data';
  } finally {
    loading.value = false;
  }
};

const loadMessages = async () => {
  try {
    const { data, error: fetchError } = await supabase
      .from('messages')
      .select('*')
      .eq('analysis_id', props.analysisId)
      .order('created_at', { ascending: true });
    
    if (fetchError) throw fetchError;
    
    messages.value = data || [];
    
    // If no messages, add a welcome message
    if (data.length === 0 && analysis.value) {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData?.user) return;
      
      const welcomeMessage = `Hi! I've analyzed ${analysis.value.url} and found some interesting insights. The website scored ${analysis.value.score}/10 overall. What would you like to know more about? I can discuss SEO improvements, content suggestions, user experience enhancements, or technical optimizations.`;
      
      const { data: msgData, error: msgError } = await supabase
        .from('messages')
        .insert({
          analysis_id: props.analysisId,
          user_id: userData.user.id,
          content: welcomeMessage,
          role: 'assistant'
        })
        .select()
        .single();
      
      if (msgError) throw msgError;
      
      messages.value = [msgData];
    }
  } catch (e) {
    console.error('Error loading messages:', e);
    error.value = 'Failed to load chat messages';
  }
};

const scrollToBottom = () => {
  setTimeout(() => {
    messageEnd.value?.scrollIntoView({ behavior: 'smooth' });
  }, 100);
};

const sendMessage = async () => {
  if (!newMessage.value.trim() || sending.value || !analysis.value) return;
  
  try {
    sending.value = true;
    
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) return;
    
    // Save user message
    const { data: userMsgData, error: userMsgError } = await supabase
      .from('messages')
      .insert({
        analysis_id: props.analysisId,
        user_id: userData.user.id,
        content: newMessage.value,
        role: 'user'
      })
      .select()
      .single();
    
    if (userMsgError) throw userMsgError;
    
    messages.value.push(userMsgData);
    
    // Clear input
    const userQuery = newMessage.value;
    newMessage.value = '';
    
    // Format messages for AI
    const chatMessages = messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
    
    // Get AI response
    const websiteContext = {
      url: analysis.value.url,
      analysis: {
        seoScore: analysis.value.analysis_data?.seoScore || 5,
        contentQuality: analysis.value.analysis_data?.contentQuality || 5,
        userExperience: analysis.value.analysis_data?.userExperience || 5,
        technicalScore: analysis.value.analysis_data?.technicalScore || 5,
        overallScore: analysis.value.score,
        seoSuggestions: analysis.value.recommendations.slice(0, 3),
        contentSuggestions: analysis.value.recommendations.slice(3, 6),
        uxSuggestions: analysis.value.recommendations.slice(6, 9),
        technicalSuggestions: analysis.value.recommendations.slice(9),
        summary: analysis.value.analysis_data?.summary || 'Website analysis completed',
        strengths: analysis.value.pros,
        weaknesses: analysis.value.cons
      },
      content: analysis.value.analysis_data?.scrapedData?.content || ''
    };
    
    const aiResponse = await chatWithAI(chatMessages, websiteContext);
    
    // Save AI response
    const { data: aiMsgData, error: aiMsgError } = await supabase
      .from('messages')
      .insert({
        analysis_id: props.analysisId,
        user_id: userData.user.id,
        content: aiResponse,
        role: 'assistant'
      })
      .select()
      .single();
    
    if (aiMsgError) throw aiMsgError;
    
    messages.value.push(aiMsgData);
    
  } catch (e) {
    console.error('Error sending message:', e);
    error.value = 'Failed to send message. Please try again.';
  } finally {
    sending.value = false;
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', { 
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  }).format(date);
};
</script>

<template>
  <!-- Clean chat interface matching reference design -->
  <div class="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
    <div class="max-w-7xl mx-auto px-6 py-4">
      <div class="flex items-center space-x-4">
        <a href="/dashboard/analysis/123" class="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          <span>Back to Analysis</span>
        </a>
        <span class="text-gray-400">•</span>
        <span class="text-gray-600 dark:text-gray-400">Discussing: Startup Homepage</span>
      </div>
    </div>
  </div>
  
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-6 py-8">
      <!-- AI Assistant header -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <h1 class="text-lg font-semibold text-gray-900 dark:text-white">AI Assistant</h1>
        </div>
      </div>
    
      <!-- Chat messages -->
      <div class="space-y-6 mb-8">
        <!-- AI message -->
        <div class="flex items-start space-x-4">
          <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
            <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <div class="flex-1">
            <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 max-w-3xl">
              <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                Hi! I'm your AI assistant for ConvertIQ. I've analyzed your landing page "Startup Homepage" and I'm here to help you understand the recommendations and answer any questions about improving your conversion rate. What would you like to know?
              </p>
            </div>
            <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">07:18 PM</div>
          </div>
        </div>
      </div>
      
      <!-- Suggested questions -->
      <div class="mb-8">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Suggested questions:</p>
        <div class="flex flex-wrap gap-3">
          <button class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            How can I improve my headline?
          </button>
          <button class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            What makes a good call-to-action button?
          </button>
          <button class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            How do I optimize for mobile users?
          </button>
          <button class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            What trust signals should I add?
          </button>
        </div>
      </div>
      
      <!-- Message input -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div class="flex items-end space-x-4">
          <div class="flex-1">
            <textarea
              placeholder="Ask about your analysis recommendations..."
              class="w-full resize-none bg-transparent border-none focus:outline-none text-gray-700 dark:text-gray-300 placeholder-gray-500 dark:placeholder-gray-400"
              rows="1"
            ></textarea>
          </div>
          <button class="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>