<script setup lang="ts">
import { ref, computed } from 'vue';
import { supabase } from '../lib/supabase';
import { scrapeWebsite, validateUrl, normalizeUrl } from '../lib/webscraper';
import { analyzeWebsiteContent } from '../lib/openrouter';
import type { ScrapedWebsite } from '../lib/webscraper';
import type { WebsiteAnalysis } from '../lib/openrouter';

const url = ref('');
const loading = ref(false);
const error = ref('');
const scrapedData = ref<ScrapedWebsite | null>(null);
const analysis = ref<WebsiteAnalysis | null>(null);
const analysisId = ref<string | null>(null);

const isValidUrl = computed(() => {
  if (!url.value.trim()) return false;
  const validation = validateUrl(normalizeUrl(url.value));
  return validation.isValid;
});

const analyzeWebsite = async () => {
  const normalizedUrl = normalizeUrl(url.value);
  const validation = validateUrl(normalizedUrl);
  
  if (!validation.isValid) {
    error.value = validation.error || 'Invalid URL';
    return;
  }
  
  try {
    loading.value = true;
    error.value = '';
    scrapedData.value = null;
    analysis.value = null;
    
    // Check if user is authenticated
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) {
      error.value = 'Please sign in to analyze websites';
      return;
    }
    
    // Step 1: Scrape website
    console.log('Scraping website...');
    const scraped = await scrapeWebsite(normalizedUrl);
    scrapedData.value = scraped;
    
    // Step 2: Analyze with AI
    console.log('Analyzing with AI...');
    const aiAnalysis = await analyzeWebsiteContent(
      scraped.url,
      scraped.content,
      scraped.title,
      scraped.headings,
      scraped.images
    );
    analysis.value = aiAnalysis;
    
    // Step 3: Save to database
    console.log('Saving to database...');
    const { data: savedAnalysis, error: saveError } = await supabase
      .from('analyses')
      .insert({
        user_id: userData.user.id,
        url: scraped.url,
        title: scraped.title,
        score: aiAnalysis.overallScore,
        conversion_rate: aiAnalysis.seoScore * 10, // Convert to percentage
        pros: aiAnalysis.strengths,
        cons: aiAnalysis.weaknesses,
        recommendations: [
          ...aiAnalysis.seoSuggestions,
          ...aiAnalysis.contentSuggestions,
          ...aiAnalysis.uxSuggestions,
          ...aiAnalysis.technicalSuggestions
        ],
        target_audience: 'General web users', // Could be enhanced with AI
        adaptations: aiAnalysis.seoSuggestions,
        analysis_data: {
          seoScore: aiAnalysis.seoScore,
          contentQuality: aiAnalysis.contentQuality,
          userExperience: aiAnalysis.userExperience,
          technicalScore: aiAnalysis.technicalScore,
          summary: aiAnalysis.summary,
          scrapedData: scraped
        }
      })
      .select('id')
      .single();
    
    if (saveError) {
      console.error('Error saving analysis:', saveError);
      throw new Error('Failed to save analysis');
    }
    
    analysisId.value = savedAnalysis.id;
    
  } catch (e) {
    console.error('Error analyzing website:', e);
    error.value = e instanceof Error ? e.message : 'An error occurred while analyzing the website';
  } finally {
    loading.value = false;
  }
};

const getScoreColor = (score: number) => {
  if (score >= 8) return 'text-green-600';
  if (score >= 6) return 'text-yellow-600';
  if (score >= 4) return 'text-orange-600';
  return 'text-red-600';
};

const getScoreLabel = (score: number) => {
  if (score >= 8) return 'Excellent';
  if (score >= 6) return 'Good';
  if (score >= 4) return 'Fair';
  return 'Needs Improvement';
};
</script>

<template>
  <div class="max-w-4xl mx-auto p-6">
    <!-- URL Input Section -->
    <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Website Analysis Platform</h1>
        <p class="text-gray-600">Enter any website URL to get comprehensive AI-powered analysis and insights</p>
      </div>
      
      <div class="max-w-2xl mx-auto">
        <div class="flex flex-col space-y-4">
          <div class="relative">
            <input
              v-model="url"
              type="text"
              placeholder="https://example.com"
              class="w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :disabled="loading"
              @keyup.enter="analyzeWebsite"
            >
            <div v-if="url && !isValidUrl" class="absolute right-3 top-3">
              <svg class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div v-else-if="url && isValidUrl" class="absolute right-3 top-3">
              <svg class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          
          <button
            @click="analyzeWebsite"
            :disabled="loading || !isValidUrl"
            class="w-full py-3 px-6 bg-blue-600 text-white text-lg font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Analyzing Website...
            </span>
            <span v-else>Analyze Website</span>
          </button>
        </div>
        
        <div v-if="error" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex">
            <svg class="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <p class="text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Website Preview -->
    <div v-if="scrapedData && !loading" class="bg-white rounded-lg shadow-lg p-6 mb-8">
      <h2 class="text-xl font-bold text-gray-900 mb-4">Website Preview</h2>
      <div class="border border-gray-300 rounded-lg overflow-hidden">
        <div class="bg-gray-100 px-4 py-2 border-b border-gray-300">
          <div class="flex items-center space-x-2">
            <div class="flex space-x-1">
              <div class="w-3 h-3 bg-red-500 rounded-full"></div>
              <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div class="flex-1 bg-white rounded px-3 py-1 text-sm text-gray-600">
              {{ scrapedData.url }}
            </div>
          </div>
        </div>
        <iframe
          :src="scrapedData.url"
          class="w-full h-96"
          sandbox="allow-scripts allow-same-origin"
          loading="lazy"
        ></iframe>
      </div>
    </div>

    <!-- Analysis Results -->
    <div v-if="analysis && !loading" class="bg-white rounded-lg shadow-lg p-6 mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-6">Analysis Results</h2>
      
      <!-- Overall Score -->
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold" :class="getScoreColor(analysis.overallScore)">
            {{ analysis.overallScore }}/10
          </div>
          <div class="text-sm text-gray-600 mt-1">Overall</div>
          <div class="text-xs font-medium mt-1" :class="getScoreColor(analysis.overallScore)">
            {{ getScoreLabel(analysis.overallScore) }}
          </div>
        </div>
        
        <div class="bg-blue-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{{ analysis.seoScore }}/10</div>
          <div class="text-sm text-gray-600 mt-1">SEO</div>
        </div>
        
        <div class="bg-green-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{{ analysis.contentQuality }}/10</div>
          <div class="text-sm text-gray-600 mt-1">Content</div>
        </div>
        
        <div class="bg-purple-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-purple-600">{{ analysis.userExperience }}/10</div>
          <div class="text-sm text-gray-600 mt-1">UX</div>
        </div>
        
        <div class="bg-orange-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-orange-600">{{ analysis.technicalScore }}/10</div>
          <div class="text-sm text-gray-600 mt-1">Technical</div>
        </div>
      </div>

      <!-- Summary -->
      <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-3">Summary</h3>
        <p class="text-gray-700 leading-relaxed">{{ analysis.summary }}</p>
      </div>

      <!-- Strengths and Weaknesses -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
            <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Strengths
          </h3>
          <ul class="space-y-2">
            <li v-for="strength in analysis.strengths" :key="strength" class="flex items-start">
              <svg class="h-4 w-4 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="text-gray-700">{{ strength }}</span>
            </li>
          </ul>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
            <svg class="h-5 w-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            Areas for Improvement
          </h3>
          <ul class="space-y-2">
            <li v-for="weakness in analysis.weaknesses" :key="weakness" class="flex items-start">
              <svg class="h-4 w-4 text-red-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
              <span class="text-gray-700">{{ weakness }}</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Detailed Suggestions -->
      <div class="space-y-6">
        <div v-if="analysis.seoSuggestions.length > 0">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">SEO Recommendations</h3>
          <ul class="space-y-2">
            <li v-for="suggestion in analysis.seoSuggestions" :key="suggestion" class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                SEO
              </div>
              <span class="text-gray-700">{{ suggestion }}</span>
            </li>
          </ul>
        </div>

        <div v-if="analysis.contentSuggestions.length > 0">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Content Recommendations</h3>
          <ul class="space-y-2">
            <li v-for="suggestion in analysis.contentSuggestions" :key="suggestion" class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                C
              </div>
              <span class="text-gray-700">{{ suggestion }}</span>
            </li>
          </ul>
        </div>

        <div v-if="analysis.uxSuggestions.length > 0">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">User Experience Recommendations</h3>
          <ul class="space-y-2">
            <li v-for="suggestion in analysis.uxSuggestions" :key="suggestion" class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                UX
              </div>
              <span class="text-gray-700">{{ suggestion }}</span>
            </li>
          </ul>
        </div>

        <div v-if="analysis.technicalSuggestions.length > 0">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Technical Recommendations</h3>
          <ul class="space-y-2">
            <li v-for="suggestion in analysis.technicalSuggestions" :key="suggestion" class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                T
              </div>
              <span class="text-gray-700">{{ suggestion }}</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="mt-8 flex flex-wrap gap-4">
        <a
          v-if="analysisId"
          :href="`/dashboard/analysis/${analysisId}`"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
          Chat About This Analysis
        </a>
        
        <a
          href="/dashboard"
          class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          View Dashboard
        </a>
      </div>
    </div>
  </div>
</template>