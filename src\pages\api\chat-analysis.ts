import type { APIRoute } from 'astro';

export const prerender = false;

const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

if (!OPENROUTER_API_KEY) {
  throw new Error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
}

const models = {
  'qwen': 'qwen/qwen-2.5-72b-instruct:free',
  'claude-sonnet': 'anthropic/claude-3.5-sonnet',
  'gpt-4o': 'openai/gpt-4o',
};

const activeModel = models['qwen'];

export const POST: APIRoute = async ({ request }) => {
  try {
    const { message, analysisData } = await request.json();

    if (!message || !analysisData) {
      return new Response(
        JSON.stringify({ error: 'Message and analysis data are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Prepare context about the website analysis
    const contextPrompt = `
You are an expert website optimization consultant. You are having a conversation with a user about their website analysis.

Website URL: ${analysisData.url}
Overall Score: ${analysisData.score}/10
Performance Score: ${analysisData.performanceScore || 'Not available'}
SEO Score: ${analysisData.seoScore || 'Not available'}

Website Strengths (Pros):
${Array.isArray(analysisData.pros) ? analysisData.pros.map((pro: string) => `- ${pro}`).join('\n') : 'No specific pros available'}

Website Weaknesses (Cons):
${Array.isArray(analysisData.cons) ? analysisData.cons.map((con: string) => `- ${con}`).join('\n') : 'No specific cons available'}

Recommendations:
${Array.isArray(analysisData.recommendations) ? analysisData.recommendations.map((rec: string) => `- ${rec}`).join('\n') : 'No specific recommendations available'}

Target Audience: ${analysisData.targetAudience || 'Not specified'}

Based on this analysis data, provide helpful, specific, and actionable advice. Be conversational but professional. 
Focus on practical steps the user can take to improve their website. If they ask about something not covered in the analysis, 
provide general best practices but acknowledge the limitation.

Keep responses concise but informative (2-4 paragraphs max). Use bullet points when listing multiple items.
`;

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: contextPrompt
          },
          {
            role: "user",
            content: message
          }
        ],
        max_tokens: 1000,
        temperature: 0.7,
      })
    });

    const data = await response.json();

    if (data.error) {
      console.error('OpenRouter API Error:', data.error);
      throw new Error(data.error.message || 'An unknown error occurred with the OpenRouter API.');
    }

    if (!data.choices || data.choices.length === 0) {
      console.error('Invalid response from OpenRouter:', data);
      throw new Error('Received an invalid response from the AI.');
    }

    const aiResponse = data.choices[0].message.content || 'I apologize, but I couldn\'t generate a response. Please try again.';

    return new Response(JSON.stringify({ 
      response: aiResponse,
      success: true 
    }), { 
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in chat analysis:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Failed to process chat message',
      response: 'I apologize, but I\'m having trouble processing your request right now. Please try again in a moment.'
    }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
