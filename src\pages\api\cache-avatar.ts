import type { APIRoute } from 'astro';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  import.meta.env.PUBLIC_SUPABASE_URL,
  import.meta.env.PUBLIC_SUPABASE_ANON_KEY  
);

export const POST: APIRoute = async ({ request }) => {
  try {
    const { avatarUrl, userId } = await request.json();

    if (!avatarUrl || !userId) {
      return new Response(JSON.stringify({ error: 'Missing avatarUrl or userId' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 1. Fetch the image from the original URL
    const response = await fetch(avatarUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch avatar from ${avatarUrl}: ${response.statusText}`);
    }

    const imageBuffer = await response.arrayBuffer();

    // 2. Upload to Supabase Storage
    const filePath = `avatars/${userId}.png`; // Assuming PNG, adjust if needed
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('avatars') // Ensure you have a bucket named 'avatars' in Supabase Storage
      .upload(filePath, imageBuffer, {
        contentType: response.headers.get('content-type') || 'image/png',
        upsert: true, // Overwrite if already exists
      });

    if (uploadError) {
      throw new Error(`Supabase Storage upload failed: ${uploadError.message}`);
    }

    // 3. Get public URL
    const { data: publicUrlData } = supabase.storage
      .from('avatars')
      .getPublicUrl(filePath);

    if (!publicUrlData) {
      throw new Error('Failed to get public URL for uploaded avatar');
    }

    const newAvatarUrl = publicUrlData.publicUrl;

    // 4. Update user's profile in the database
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ avatar_url: newAvatarUrl })
      .eq('id', userId);

    if (updateError) {
      throw new Error(`Supabase profile update failed: ${updateError.message}`);
    }

    return new Response(JSON.stringify({ newAvatarUrl }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error caching avatar:', error);
    return new Response(JSON.stringify({
      error: 'Failed to cache avatar',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
