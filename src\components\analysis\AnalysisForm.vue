<script setup lang="ts">
import { ref, computed } from 'vue';
import { supabase } from '../../lib/supabase';
import { CircleCheckBig, Rocket, Globe, TrendingUp, CircleGauge } from 'lucide-vue-next';

const url = ref('');
const isSubmitting = ref(false);
const error = ref<string | null>(null);
const success = ref(false);

// URL validation that accepts various formats
const isValidUrl = computed(() => {
  if (!url.value.trim()) return false;

  const trimmedUrl = url.value.trim();

  // Check if it's already a valid URL
  try {
    const urlObj = new URL(trimmedUrl);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    // If not a valid URL, check if it's a valid domain
    return isValidDomain(trimmedUrl);
  }
});

// Helper function to validate domain formats
const isValidDomain = (input: string): boolean => {
  // Remove protocol if present
  let domain = input.replace(/^https?:\/\//, '');

  // Remove www. if present
  domain = domain.replace(/^www\./, '');

  // Remove trailing slash and path
  domain = domain.split('/')[0];

  // Basic domain validation regex
  const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  return domainRegex.test(domain) && domain.includes('.');
};

// Normalize URL by adding https:// if missing
const normalizeUrl = (input: string): string => {
  const trimmedUrl = input.trim();

  // If already has protocol, return as is
  if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
    return trimmedUrl;
  }

  // Add https:// protocol
  return `https://${trimmedUrl}`;
};

const canSubmit = computed(() => {
  return isValidUrl.value && !isSubmitting.value;
});

const submitAnalysis = async () => {
  if (!canSubmit.value) return;

  try {
    isSubmitting.value = true;
    error.value = null;

    // Normalize the URL before processing
    const normalizedUrlValue = normalizeUrl(url.value);

    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      throw new Error('You must be logged in to create an analysis');
    }

    // Create analysis record in the real database
    const { data: analysis, error: analysisError } = await supabase
      .from('analyses')
      .insert({
        user_id: userData.user.id,
        url: normalizedUrlValue,
        title: `Analysis of ${normalizedUrlValue}`,
        score: 0, // Will be updated when analysis completes
        conversion_rate: 0, // Will be updated when analysis completes
        pros: '[]',
        cons: '[]',
        recommendations: '[]',
        target_audience: '',
        adaptations: '[]'
      })
      .select()
      .single();

    if (analysisError) {
      throw analysisError;
    }

    // Start the analysis process with real APIs
    await startAnalysisProcess(analysis.id, normalizedUrlValue);

    success.value = true;

    // Redirect to analysis page after a short delay
    setTimeout(() => {
      window.location.href = `/dashboard/analysis/${analysis.id}`;
    }, 2000);

  } catch (err) {
    console.error('Error creating analysis:', err);
    error.value = err instanceof Error ? err.message : 'Failed to create analysis';
  } finally {
    isSubmitting.value = false;
  }
};



const startAnalysisProcess = async (analysisId: string, url: string) => {
  try {
    // Start performance and SEO analysis in background (don't wait for completion)
    Promise.all([
      fetch('/api/analyze-performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url, analysisId })
      }).catch(error => {
        console.error('Performance analysis failed:', error);
      }),
      fetch('/api/analyze-seo', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url, analysisId })
      }).catch(error => {
        console.error('SEO analysis failed:', error);
      }),
      fetch('/api/comprehensive-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url, analysisId })
      }).catch(error => {
        console.error('Comprehensive analysis failed:', error);
      })
    ]).then(() => {
      console.log('Background analysis processes started');
    });

  } catch (error) {
    console.error('Error in analysis process:', error);
  }
};

const handleUrlInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  url.value = target.value;
  error.value = null;
};
</script>

<template>
  <div class="bg-background-secondary flex justify-center px-6 py-20">
    <div class="w-full max-w-2xl">
      <!-- Success State -->
      <div v-if="success" class="text-center">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-success-50 rounded-2xl mb-6">
          <CircleCheckBig class="w-8 h-8 text-success-600"/>
        </div>
        
        <h1 class="text-4xl font-bold text-gray-900 mb-4">
          Analysis Started!
        </h1>
        <p class="text-xl text-gray-600 mb-8">
          Your landing page analysis is now processing. You'll be redirected to view the results shortly.
        </p>
        
        <div class="flex items-center justify-center space-x-2 text-gray-500">
          <svg class="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Processing...</span>
        </div>
      </div>

      <!-- Form State -->
      <div v-else>
        <!-- Icon and header -->
        <div class="text-center mb-12"><!--
          <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-10">
            <FilePlus class="w-8 h-8 text-white"/>
          </div>-->
          <img src="/public/logo.svg" class='w-20 h-20 flex items-center justify-center mx-auto mb-10' />
          
          <h1 class="text-4xl font-bold text-gray-900 mb-4">
            Analyze Your Landing Page
          </h1>
          <p class="text-xl text-gray-600">
            Get AI-powered insights and actionable recommendations to boost your conversion rates
          </p>
        </div>

        <!-- URL Input Form -->
        <form @submit.prevent="submitAnalysis" class="card p-8 mb-8">
          <div class="space-y-6">
            <div>
              <label for="url" class="block text-sm font-medium text-gray-700 mb-3">
                Website URL
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Globe class="h-5 w-5 text-gray-400"/>
                </div>
                <input
                  id="url"
                  type="text"
                  :value="url"
                  @input="handleUrlInput"
                  placeholder="https://your-landing-page.com"
                  class="input-field-lg pl-12 outline-none"
                  :class="{ 
                    'border-error-300 focus:border-error-500 focus:ring-error-500': error,
                    'border-success-300 focus:border-success-500 focus:ring-success-500': url && isValidUrl
                  }"
                  :disabled="isSubmitting"
                >
              </div>
              
              <!-- Error Message -->
              <p v-if="error" class="mt-2 text-sm text-error-600">
                {{ error }}
              </p>
              
              <!-- Help Text -->
              <p v-else class="mt-2 text-sm text-gray-500">
                Enter the URL of the landing page you want to analyze
              </p>
            </div>
            
            <button
              type="submit"
              :disabled="!canSubmit"
              class="w-full btn-primary py-4 text-base"
              :class="{ 
                'opacity-50 cursor-not-allowed': !canSubmit,
                'opacity-75': isSubmitting
              }"
            >
              <svg 
                v-if="isSubmitting" 
                class="animate-spin w-5 h-5 mr-2" 
                fill="none" 
                viewBox="0 0 24 24"
              >
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <Rocket v-else class="w-5 h-5 mr-2"/>
              <span>{{ isSubmitting ? 'Starting Analysis...' : 'Start Analysis' }}</span>
            </button>
          </div>
        </form>
        
        <!-- Feature cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="card p-6">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center">
                <TrendingUp class="w-5 h-5 text-primary-600"/>
              </div>
              <h3 class="font-semibold text-gray-900">Conversion Optimization</h3>
            </div>
            <p class="text-gray-600">
              Get actionable insights to improve your conversion rates
            </p>
          </div>

          <div class="card p-6">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center">
                <CircleGauge class="w-5 h-5 text-primary-600"/>
              </div>
              <h3 class="font-semibold text-gray-900">Performance Analysis</h3>
            </div>
            <p class="text-gray-600">
              Detailed analysis of your landing page performance
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
