// API endpoint for running Lighthouse performance analysis
import type { APIRoute } from 'astro';
// @ts-ignore
import puppeteer from 'puppeteer';
// @ts-ignore
import lighthouse from 'lighthouse';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  import.meta.env.PUBLIC_SUPABASE_URL,
  import.meta.env.SUPABASE_SERVICE_ROLE_KEY
);

export const POST: APIRoute = async ({ request }) => {
  let browser;

  try {
    const { url, analysisId } = await request.json();

    if (!url || !analysisId) {
      return new Response(JSON.stringify({ error: 'Missing required parameters' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Normalize URL
    const normalizedUrl = url.startsWith('http') ? url : `https://${url}`;

    // Launch browser with optimized settings
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    // Take screenshot first
    const page = await browser.newPage();
    await page.setViewport({ width: 1200, height: 800 });

    try {
      await page.goto(normalizedUrl, { waitUntil: 'networkidle2', timeout: 30000 });

      // Take screenshot
      const screenshot = await page.screenshot({
        type: 'png',
        fullPage: false,
        clip: { x: 0, y: 0, width: 1200, height: 800 }
      });

      // Upload screenshot to Supabase Storage
      const screenshotPath = `public/${analysisId}.png`;
      const { error: uploadError } = await supabase.storage
        .from('screenshots')
        .upload(screenshotPath, screenshot, {
          contentType: 'image/png',
          upsert: true,
        });

      if (uploadError) {
        throw new Error(`Screenshot upload failed: ${uploadError.message}`);
      }

      // Get public URL for the uploaded screenshot
      const { data: publicUrlData } = supabase.storage
        .from('screenshots')
        .getPublicUrl(screenshotPath);

      if (!publicUrlData) {
        throw new Error('Failed to get public URL for screenshot');
      }

      // Store the public URL in the database
      await supabase
        .from('analyses')
        .update({ screenshot_url: publicUrlData.publicUrl })
        .eq('id', analysisId);

    } catch (screenshotError) {
      console.warn('Screenshot capture failed:', screenshotError);
    }

    await page.close();

    // Run Lighthouse audit
    const { lhr } = await lighthouse(normalizedUrl, {
      port: new URL(browser.wsEndpoint()).port,
      output: 'json',
      logLevel: 'error',
      onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
      settings: {
        maxWaitForFcp: 15 * 1000,
        maxWaitForLoad: 35 * 1000,
        formFactor: 'desktop',
        throttling: {
          rttMs: 40,
          throughputKbps: 10240,
          cpuSlowdownMultiplier: 1,
          requestLatencyMs: 0,
          downloadThroughputKbps: 0,
          uploadThroughputKbps: 0
        },
        screenEmulation: {
          mobile: false,
          width: 1200,
          height: 800,
          deviceScaleFactor: 1,
          disabled: false
        }
      }
    });

    // Extract metrics
    const performanceScore = Math.round((lhr.categories.performance?.score || 0) * 100);
    const accessibilityScore = Math.round((lhr.categories.accessibility?.score || 0) * 100);
    const bestPracticesScore = Math.round((lhr.categories['best-practices']?.score || 0) * 100);
    const seoScore = Math.round((lhr.categories.seo?.score || 0) * 100);

    // Core Web Vitals
    const lcpScore = (lhr.audits['largest-contentful-paint']?.numericValue || 0) / 1000;
    const fidScore = lhr.audits['max-potential-fid']?.numericValue || 0;
    const clsScore = lhr.audits['cumulative-layout-shift']?.numericValue || 0;

    // Store results in database
    const { error: updateError } = await supabase
      .from('analyses')
      .update({
        performance_score: performanceScore,
        lcp_score: lcpScore,
        fid_score: fidScore,
        cls_score: clsScore,
        seo_score: seoScore,
        lighthouse_data: lhr,
        accessibility_score: accessibilityScore,
        best_practices_score: bestPracticesScore
      })
      .eq('id', analysisId);

    if (updateError) {
      console.error('Database update error:', updateError);
    }

    await browser.close();

    return new Response(JSON.stringify({
      success: true,
      performanceScore,
      seoScore,
      accessibilityScore,
      bestPracticesScore,
      coreWebVitals: {
        lcp: Math.round(lcpScore * 100) / 100,
        fid: Math.round(fidScore),
        cls: Math.round(clsScore * 1000) / 1000
      },
      lighthouseData: lhr
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Performance analysis error:', error);

    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error('Browser close error:', closeError);
      }
    }

    return new Response(JSON.stringify({
      error: 'Analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/*
Package.json dependencies for real implementation:

{
  "dependencies": {
    "puppeteer": "^21.0.0",
    "lighthouse": "^11.0.0",
    "chrome-launcher": "^1.0.0"
  }
}

Installation command:
npm install puppeteer lighthouse chrome-launcher

Docker considerations:
- Use puppeteer/puppeteer Docker image for containerized deployments
- Install Chrome dependencies in your Dockerfile
- Set appropriate Chrome flags for headless operation

Environment variables:
- CHROME_BIN: Path to Chrome binary (for production)
- LIGHTHOUSE_FLAGS: Additional Lighthouse configuration
- PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: Skip Chromium download if using system Chrome

Example Dockerfile additions:
RUN apt-get update && apt-get install -y \
  chromium-browser \
  --no-install-recommends \
  && rm -rf /var/lib/apt/lists/*

ENV CHROME_BIN=/usr/bin/chromium-browser
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
*/
