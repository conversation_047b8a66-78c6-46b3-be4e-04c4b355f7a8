// API endpoint for running Lighthouse performance analysis
import type { APIRoute } from 'astro';
import { createClient } from '@supabase/supabase-js';
import { log } from '../../lib/logger';

export const prerender = false;

const supabase = createClient(
  import.meta.env.PUBLIC_SUPABASE_URL,
  import.meta.env.SUPABASE_SERVICE_ROLE_KEY
);

export const POST: APIRoute = async ({ request }) => {
  let browser;

  try {
    const { url, analysisId } = await request.json();

    log.info(`Received performance analysis request for URL: ${url} with analysisId: ${analysisId}`);

    if (!url || !analysisId) {
      log.warn('Missing URL or analysisId in performance analysis request');
      return new Response(JSON.stringify({ error: 'Missing required parameters' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Normalize URL
    const normalizedUrl = url.startsWith('http') ? url : `https://${url}`;
    log.debug(`Normalized URL: ${normalizedUrl}`);

    // Dynamic import of Puppeteer and Lighthouse to avoid build issues
    log.debug('Dynamically importing Puppeteer and Lighthouse...');
    const puppeteer = await import('puppeteer');
    const lighthouse = await import('lighthouse');
    log.debug('Puppeteer and Lighthouse imported.');

    // Launch browser with optimized settings
    log.debug('Launching browser...');
    browser = await puppeteer.default.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });
    log.info('Browser launched.');

    // Take screenshot first
    log.debug('Creating new page and setting viewport...');
    const page = await browser.newPage();
    await page.setViewport({ width: 1200, height: 800 });

    try {
      log.debug(`Navigating to ${normalizedUrl} for screenshot...`);
      await page.goto(normalizedUrl, { waitUntil: 'networkidle2', timeout: 30000 });
      log.debug('Page loaded for screenshot.');

      // Take screenshot
      log.debug('Taking screenshot...');
      const screenshot = await page.screenshot({
        type: 'png',
        fullPage: false,
        clip: { x: 0, y: 0, width: 1200, height: 800 }
      });
      log.debug('Screenshot taken.');

      // Upload screenshot to Supabase Storage
      const screenshotPath = `public/${analysisId}.png`;
      log.debug(`Uploading screenshot to Supabase at ${screenshotPath}...`);
      const { error: uploadError } = await supabase.storage
        .from('screenshots')
        .upload(screenshotPath, screenshot, {
          contentType: 'image/png',
          upsert: true,
        });

      if (uploadError) {
        log.error(`Screenshot upload failed: ${uploadError.message}`);
        throw new Error(`Screenshot upload failed: ${uploadError.message}`);
      }
      log.info('Screenshot uploaded successfully.');

      // Get public URL for the uploaded screenshot
      log.debug('Getting public URL for screenshot...');
      const { data: publicUrlData } = supabase.storage
        .from('screenshots')
        .getPublicUrl(screenshotPath);

      if (!publicUrlData) {
        log.error('Failed to get public URL for screenshot');
        throw new Error('Failed to get public URL for screenshot');
      }
      log.debug(`Public URL: ${publicUrlData.publicUrl}`);

      // Store the public URL in the database
      log.debug('Storing screenshot URL in database...');
      await supabase
        .from('analyses')
        .update({ screenshot_url: publicUrlData.publicUrl })
        .eq('id', analysisId);
      log.info('Screenshot URL stored in database.');

    } catch (screenshotError) {
      log.warn(`Screenshot capture failed: ${screenshotError instanceof Error ? screenshotError.message : screenshotError}`);
    }

    log.debug('Closing page...');
    await page.close();
    log.debug('Page closed.');

    // Run Lighthouse audit
    log.info(`Running Lighthouse audit for ${normalizedUrl}...`);
    const { lhr } = await lighthouse.default(normalizedUrl, {
      port: new URL(browser.wsEndpoint()).port,
      output: 'json',
      logLevel: 'error',
      onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
      settings: {
        maxWaitForFcp: 15 * 1000,
        maxWaitForLoad: 35 * 1000,
        formFactor: 'desktop',
        throttling: {
          rttMs: 40,
          throughputKbps: 10240,
          cpuSlowdownMultiplier: 1,
          requestLatencyMs: 0,
          downloadThroughputKbps: 0,
          uploadThroughputKbps: 0
        },
        screenEmulation: {
          mobile: false,
          width: 1200,
          height: 800,
          deviceScaleFactor: 1,
          disabled: false
        }
      }
    });
    log.info('Lighthouse audit completed.');

    // Extract metrics
    const performanceScore = Math.round((lhr.categories.performance?.score || 0) * 100);
    const accessibilityScore = Math.round((lhr.categories.accessibility?.score || 0) * 100);
    const bestPracticesScore = Math.round((lhr.categories['best-practices']?.score || 0) * 100);
    const seoScore = Math.round((lhr.categories.seo?.score || 0) * 100);
    log.debug(`Performance Score: ${performanceScore}, Accessibility Score: ${accessibilityScore}, Best Practices Score: ${bestPracticesScore}, SEO Score: ${seoScore}`);

    // Core Web Vitals
    const lcpScore = (lhr.audits['largest-contentful-paint']?.numericValue || 0) / 1000;
    const fidScore = lhr.audits['max-potential-fid']?.numericValue || 0;
    const clsScore = lhr.audits['cumulative-layout-shift']?.numericValue || 0;
    log.debug(`Core Web Vitals - LCP: ${lcpScore}, FID: ${fidScore}, CLS: ${clsScore}`);

    // Store results in database
    log.debug('Storing Lighthouse results in database...');
    const { error: updateError } = await supabase
      .from('analyses')
      .update({
        performance_score: performanceScore,
        lcp_score: lcpScore,
        fid_score: fidScore,
        cls_score: clsScore,
        seo_score: seoScore,
        lighthouse_data: lhr,
        accessibility_score: accessibilityScore,
        best_practices_score: bestPracticesScore
      })
      .eq('id', analysisId);

    if (updateError) {
      log.error(`Database update error: ${updateError.message}`);
    }
    log.info('Lighthouse results stored in database.');

    log.debug('Closing browser...');
    await browser.close();
    log.info('Browser closed.');

    return new Response(JSON.stringify({
      success: true,
      performanceScore,
      seoScore,
      accessibilityScore,
      bestPracticesScore,
      coreWebVitals: {
        lcp: Math.round(lcpScore * 100) / 100,
        fid: Math.round(fidScore),
        cls: Math.round(clsScore * 1000) / 1000
      },
      lighthouseData: lhr
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Performance analysis error: ${error instanceof Error ? error.message : error}`);

    if (browser) {
      try {
        log.warn('Attempting to close browser after error...');
        await browser.close();
        log.warn('Browser closed after error.');
      } catch (closeError) {
        log.error(`Browser close error during cleanup: ${closeError instanceof Error ? closeError.message : closeError}`);
      }
    }

    return new Response(JSON.stringify({
      error: 'Analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/*
Package.json dependencies for real implementation:

{
  "dependencies": {
    "puppeteer": "^21.0.0",
    "lighthouse": "^11.0.0",
    "chrome-launcher": "^1.0.0"
  }
}

Installation command:
npm install puppeteer lighthouse chrome-launcher

Docker considerations:
- Use puppeteer/puppeteer Docker image for containerized deployments
- Install Chrome dependencies in your Dockerfile
- Set appropriate Chrome flags for headless operation

Environment variables:
- CHROME_BIN: Path to Chrome binary (for production)
- LIGHTHOUSE_FLAGS: Additional Lighthouse configuration
- PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: Skip Chromium download if using system Chrome

Example Dockerfile additions:
RUN apt-get update && apt-get install -y \
  chromium-browser \
  --no-install-recommends \
  && rm -rf /var/lib/apt/lists/*

ENV CHROME_BIN=/usr/bin/chromium-browser
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
*/
