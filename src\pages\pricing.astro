---
import Layout from '../layouts/Layout.astro';
import { Rocket, BriefcaseBusiness, Building, Check, ArrowLeft } from 'lucide-vue-next'
---

<Layout title="Pricing - ConvertIQ">
  <!-- Header with back navigation -->
  <div class="bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-6 py-4">
      <div class="flex items-center space-x-4">
        <a href="/dashboard" class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors">
          <ArrowLeft class="w-4 h-4 color-current"/>
          <span>Back to Dashboard</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Main pricing content -->
  <div class="min-h-screen bg-background-secondary">
    <div class="max-w-7xl mx-auto px-6 py-16">
      <!-- Header -->
      <div class="text-center mb-16">
        <h1 class="text-4xl font-bold text-gray-900 mb-6">Choose Your Plan</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Get AI-powered insights to optimize your landing pages and boost conversions
        </p>
      </div>

      <!-- Billing toggle -->
      <div class="flex items-center justify-center mb-12">
        <div class="flex items-center space-x-4">
          <span class="text-gray-600">Monthly</span>
          <div class="relative">
            <input type="checkbox" class="sr-only" id="billing-toggle">
            <label for="billing-toggle" class="flex items-center cursor-pointer">
              <div class="w-12 h-6 bg-gray-200 rounded-full relative">
                <div class="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 transition-transform duration-200 shadow-sm"></div>
              </div>
            </label>
          </div>
          <span class="text-gray-600">Yearly</span>
          <span class="px-3 py-1 bg-success-50 text-success-600 text-sm rounded-full font-medium">Save up to 20%</span>
        </div>
      </div>

      <!-- Pricing cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <!-- Starter Plan -->
        <div class="card p-8">
          <div class="text-center mb-8">
            <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Rocket class='w-6 h-6 text-gray-600'/>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-2">Starter</h3>
            <p class="text-gray-600 mb-6">Perfect for individuals and small projects</p>
            <div class="mb-6">
              <span class="text-4xl font-bold text-gray-900">$29</span>
              <span class="text-gray-600">/month</span>
            </div>
          </div>

          <ul class="space-y-4 mb-8">
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">5 analyses per month</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Basic AI recommendations</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Screenshot capture</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Email support</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Export reports (PDF)</span>
            </li>
          </ul>

          <button class="btn-secondary w-full py-3">
            Start Free Trial
          </button>
        </div>

        <!-- Professional Plan (Most Popular) -->
        <div class="card p-8 relative border-2 border-primary-500">
          <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <span class="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
          </div>

          <div class="text-center mb-8">
            <div class="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <BriefcaseBusiness class='h-6 w-6 text-white'/>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-2">Professional</h3>
            <p class="text-gray-600 mb-6">Ideal for growing businesses and agencies</p>
            <div class="mb-6">
              <span class="text-4xl font-bold text-gray-900">$79</span>
              <span class="text-gray-600">/month</span>
            </div>
          </div>

          <ul class="space-y-4 mb-8">
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Unlimited analyses</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Advanced AI insights</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Priority chat support</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Custom branding</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">API access</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Team collaboration</span>
            </li>
          </ul>

          <button class="btn-primary w-full py-3">
            Start Free Trial
          </button>
        </div>

        <!-- Enterprise Plan -->
        <div class="card p-8">
          <div class="text-center mb-8">
            <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Building class='text-gray-600 w-6 h-6'/>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
            <p class="text-gray-600 mb-6">Custom solutions for large organizations</p>
            <div class="mb-6">
              <span class="text-4xl font-bold text-gray-900">$299</span>
              <span class="text-gray-600">/month</span>
            </div>
          </div>

          <ul class="space-y-4 mb-8">
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Everything in Professional</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">White-label solution</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Dedicated account manager</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">Custom integrations</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-5 h-5 text-success-500" />
              <span class="text-gray-700">SLA guarantee</span>
            </li>
          </ul>

          <button class="btn-secondary w-full py-3">
            Contact Sales
          </button>
        </div>
      </div>
    </div>
  </div>
</Layout>