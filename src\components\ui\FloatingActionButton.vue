<script setup lang="ts">
import { ref } from 'vue';

const isHovered = ref(false);
</script>

<template>
  <div class="fixed bottom-6 right-6 z-50">
    <a
      href="/dashboard/analysis/new"
      @mouseenter="isHovered = true"
      @mouseleave="isHovered = false"
      class="group relative flex items-center justify-center w-14 h-14 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
    >
      <!-- Plus icon -->
      <svg
        class="w-6 h-6 text-white transition-transform duration-300 group-hover:rotate-90"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
        />
      </svg>
      
      <!-- Tooltip -->
      <div
        :class="[
          'absolute right-full mr-3 px-3 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-sm rounded-lg whitespace-nowrap transition-all duration-200',
          isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2 pointer-events-none'
        ]"
      >
        New Analysis
        <div class="absolute top-1/2 left-full w-0 h-0 border-l-4 border-l-gray-900 dark:border-l-gray-100 border-y-4 border-y-transparent transform -translate-y-1/2"></div>
      </div>
      
      <!-- Ripple effect -->
      <div class="absolute inset-0 rounded-full bg-white/20 scale-0 group-hover:scale-100 transition-transform duration-300"></div>
    </a>
  </div>
</template>