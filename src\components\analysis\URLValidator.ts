export function validateURL(url: string): { isValid: boolean; error?: string } {
  if (!url.trim()) {
    return { isValid: false, error: 'Please enter a URL' };
  }

  try {
    const urlObj = new URL(url);
    
    // Check if it's HTTP or HTTPS
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return { isValid: false, error: 'URL must use HTTP or HTTPS protocol' };
    }
    
    // Check if hostname exists
    if (!urlObj.hostname) {
      return { isValid: false, error: 'Invalid hostname' };
    }
    
    // Check for localhost or private IPs (basic check)
    if (urlObj.hostname === 'localhost' || 
        urlObj.hostname.startsWith('127.') || 
        urlObj.hostname.startsWith('192.168.') ||
        urlObj.hostname.startsWith('10.') ||
        urlObj.hostname.includes('internal')) {
      return { isValid: false, error: 'Cannot analyze local or private URLs' };
    }
    
    return { isValid: true };
  } catch (e) {
    return { isValid: false, error: 'Please enter a valid URL (e.g., https://example.com)' };
  }
}

export function normalizeURL(url: string): string {
  // Add https:// if no protocol is specified
  if (!url.match(/^https?:\/\//)) {
    url = 'https://' + url;
  }
  
  try {
    const urlObj = new URL(url);
    return urlObj.toString();
  } catch (e) {
    return url;
  }
}