<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/supabase';
import { Plus, Search, LayoutGrid, Rows3, Eye, FileText } from 'lucide-vue-next';
import Dropdown from '../ui/Dropdown.vue';

type Analysis = Database['public']['Tables']['analyses']['Row'];

const analyses = ref<Analysis[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);
const searchQuery = ref('');
const statusFilter = ref('all');
const sortBy = ref('newest');
const viewMode = ref<'grid' | 'list'>('grid');

const statusOptions = [
  { value: 'all', label: 'All Status' },
  { value: 'completed', label: 'Completed' },
  { value: 'processing', label: 'Processing' },
];

const sortOptions = [
  { value: 'newest', label: 'Newest First' },
  { value: 'oldest', label: 'Oldest First' },
  { value: 'score', label: 'Highest Score' },
];

const navigateToAnalysis = (analysisId: string) => {
  window.location.href = `/dashboard/analysis/${analysisId}`;
};

const loadAnalyses = async () => {
  try {
    loading.value = true;
    error.value = null;

    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      throw new Error('User not authenticated');
    }

    const { data: analysesData, error: analysesError } = await supabase
      .from('analyses')
      .select('*')
      .eq('user_id', userData.user.id)
      .order('created_at', { ascending: false });

    if (analysesError) {
      throw analysesError;
    }

    analyses.value = analysesData || [];
  } catch (err) {
    console.error('Error loading analyses:', err);
    error.value = err instanceof Error ? err.message : 'Failed to load analyses';
  } finally {
    loading.value = false;
  }
};

const filteredAnalyses = computed(() => {
  let filtered = [...analyses.value]; // Create a shallow copy to avoid mutation issues

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(analysis =>
      (analysis.title?.toLowerCase().includes(query)) ||
      (analysis.url?.toLowerCase().includes(query))
    );
  }

  // Status filter
  if (statusFilter.value === 'completed') {
    // @ts-ignore
    filtered = filtered.filter(analysis => analysis.score !== null);
  } else if (statusFilter.value === 'processing') {
    // @ts-ignore
    filtered = filtered.filter(analysis => analysis.score === null);
  }

  // Sort
  if (sortBy.value === 'newest') {
    filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } else if (sortBy.value === 'oldest') {
    filtered.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
  } else if (sortBy.value === 'score') {
    filtered.sort((a, b) => (b.score || 0) - (a.score || 0));
  }

  return filtered;
});

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Simple utility functions that don't cause type inference issues
const isProcessing = (analysis: Analysis) => analysis.score === null;
const statusText = (isProc: boolean) => isProc ? 'Processing' : 'Completed';
const statusClass = (isProc: boolean) => isProc ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800';

const getPerformanceGrade = (score: number | null) => {
  if (!score) return 'N/A';
  if (score >= 90) return 'A';
  if (score >= 80) return 'B';
  if (score >= 70) return 'C';
  if (score >= 60) return 'D';
  return 'F';
};

const getGradeColor = (grade: string) => {
  switch (grade) {
    case 'A': return 'bg-green-100 text-green-800';
    case 'B': return 'bg-blue-100 text-blue-800';
    case 'C': return 'bg-yellow-100 text-yellow-800';
    case 'D': return 'bg-orange-100 text-orange-800';
    case 'F': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

onMounted(() => {
  loadAnalyses();
});

</script>

<template>
  <div class="max-w-7xl mx-auto px-6 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 mb-1">Analysis History</h1>
          <p class="text-gray-600">View and manage all your landing page analyses</p>
        </div>
        <a
          href="/dashboard/analysis/new"
          class="btn-primary"
        >
          <Plus class="w-4 h-4 mr-2"/>
          New Analysis
        </a>
      </div>
    </div>

    <!-- Filters and Controls -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
          <!-- Search -->
          <div class="relative">
            <Search class='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 transition-colors duration-300'/>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search analyses..."
              class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64 transition-all duration-300 hover:border-gray-400 focus:shadow-lg"
            >
          </div>

          <!-- Status Filter -->
          <div class="w-32">
            <Dropdown
              :options="statusOptions"
              v-model="statusFilter"
            />
          </div>

          <!-- Sort -->
          <div class="w-68 pr-32">
            <Dropdown
              :options="sortOptions"
              v-model="sortBy"
            />
          </div>
        </div>

        <!-- View Mode Toggle -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500 mr-4">Showing {{ filteredAnalyses.length }} of {{ analyses.length }} analyses</span>
          <div class="flex border border-gray-300 rounded-lg">
            <button
              @click="viewMode = 'grid'"
              :class="viewMode === 'grid' ? 'bg-blue-500 text-white shadow-md' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'"
              class="p-2 rounded-l-lg transition-all duration-300 transform hover:scale-105"
            >
            <LayoutGrid class="w-4 h-4"/>
            </button>
            <button
              @click="viewMode = 'list'"
              :class="viewMode === 'list' ? 'bg-blue-500 text-white shadow-md' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'"
              class="p-2 rounded-r-lg transition-all duration-300 transform hover:scale-105"
            >
            <Rows3 class="w-4 h-4"/>
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- Loading State -->
    <div v-if="loading" class="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
      <p class="text-gray-500">Loading analyses...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
        <svg class="w-8 h-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Error loading analyses</h3>
      <p class="text-gray-500 mb-4">{{ error }}</p>
      <button @click="loadAnalyses" class="btn-primary">Try Again</button>
    </div>

    <!-- Empty State -->
    <div v-else-if="filteredAnalyses.length === 0" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <FileText class='w-8 h-8 text-gray-400'/>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No analyses found</h3>
      <p class="text-gray-500 mb-6">
        {{ searchQuery ? 'Try adjusting your search criteria' : 'Get started by analyzing your first landing page' }}
      </p>
      <a href="/dashboard/analysis/new" class="btn-primary">Create Analysis</a>
    </div>
    <!-- Grid View -->
    <div v-else-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <a
        v-for="analysis in filteredAnalyses"
        :key="analysis.id"
        :href="`/dashboard/analysis/${analysis.id}`"
        class="bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300 overflow-hidden group transform hover:-translate-y-1"
      >
        <!-- Screenshot placeholder -->
        <div class="h-32 bg-gray-100 flex items-center justify-center">
          <Eye class="w-8 h-8 text-gray-400"/>
        </div>

        <div class="p-4">
          <!-- Status Badge -->
          <div class="flex items-center justify-between mb-2">
            <span
              :class="statusClass(isProcessing(analysis))"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            >
              {{ statusText(isProcessing(analysis)) }}
            </span>
            <span class="text-xs text-gray-500">{{ formatDate(analysis.created_at) }}</span>
          </div>

          <!-- Title and URL -->
          <h3 class="font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
            {{ analysis.title || 'Untitled Analysis' }}
          </h3>
          <p class="text-sm text-gray-500 mb-3 truncate">{{ analysis.url }}</p>

          <!-- Metrics -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="text-center">
                <p class="text-xs text-gray-500">Score</p>
                <p class="text-lg font-bold text-gray-900">{{ analysis.score || 0 }}</p>
              </div>
              <div class="text-center">
                <p class="text-xs text-gray-500">Suggestions</p>
                <p class="text-lg font-bold text-blue-600">{{ analysis.performance_score || 12 }}</p>
              </div>
            </div>
            <div
              :class="getGradeColor(getPerformanceGrade(analysis.performance_score))"
              class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
            >
              {{ getPerformanceGrade(analysis.performance_score) }}
            </div>
          </div>
        </div>
      </a>
    </div>

    <!-- List View -->
    <div v-else class="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Page</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Suggestions</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="analysis in filteredAnalyses" :key="analysis.id" class="hover:bg-gray-50 cursor-pointer" @click="navigateToAnalysis(analysis.id)">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ analysis.title || 'Untitled Analysis' }}</div>
                  <div class="text-sm text-gray-500 truncate max-w-xs">{{ analysis.url }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ analysis.score || 0 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <span class="text-sm font-medium text-gray-900 mr-2">{{ analysis.performance_score || 85 }}</span>
                  <span
                    :class="getGradeColor(getPerformanceGrade(analysis.performance_score))"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  >
                    {{ getPerformanceGrade(analysis.performance_score) }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm text-blue-600 font-medium">8</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(analysis.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="statusClass(isProcessing(analysis))"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ statusText(isProcessing(analysis)) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  background-color: #2563eb;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}
</style>