# ConvertIQ Performance Analysis Setup

This guide explains how to set up the Lighthouse-based performance metrics and SEO analysis features in ConvertIQ.

## 🗄️ Database Setup

### 1. Execute Schema Updates

Run the following SQL files in your Supabase SQL Editor:

```sql
-- 1. First, run the performance schema updates
-- Execute: performance-schema-updates.sql
```

This will add:
- Performance metrics columns to the `analyses` table
- New `performance_metrics` table for detailed tracking
- New `seo_issues` table for SEO problem tracking
- Automatic triggers and functions
- Proper RLS policies

### 2. Verify Database Changes

After running the schema updates, verify the changes:

```sql
-- Check new columns in analyses table
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'analyses' 
AND column_name IN ('performance_score', 'lcp_score', 'fid_score', 'cls_score', 'seo_score');

-- Check new tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_name IN ('performance_metrics', 'seo_issues');
```

## 🚀 Frontend Features

### New Components Added

1. **PerformanceTab.vue** - Displays Core Web Vitals and Lighthouse metrics
2. **SEOAnalysisTab.vue** - Shows SEO issues and recommendations  
3. **ProsAndCons.vue** - Animated summary of all analysis results
4. **Updated AnalysisResultsNew.vue** - Tabbed interface with all analysis types

### Key Features

- **Tabbed Interface**: Suggestions, Performance, Analysis, SEO Insights
- **Core Web Vitals Display**: LCP, FID, CLS with color-coded status
- **Performance Grading**: A-F grades based on Lighthouse scores
- **Conversion Impact Estimates**: Industry-based improvement predictions
- **Advanced Metrics**: Collapsible detailed Lighthouse data
- **SEO Issue Tracking**: Categorized by severity (Critical, Warning, Info)
- **Animated Pros/Cons**: Smooth fade-in animations with comprehensive summaries

## 🔧 Backend Implementation

### For Production Use

To implement real Lighthouse and SEO analysis, you'll need to:

#### 1. Install Dependencies

```bash
npm install puppeteer lighthouse chrome-launcher
```

#### 2. Set Up Chrome/Chromium

**Local Development:**
```bash
# Chrome will be downloaded automatically by Puppeteer
```

**Production (Docker):**
```dockerfile
# Add to your Dockerfile
RUN apt-get update && apt-get install -y \
  chromium-browser \
  --no-install-recommends \
  && rm -rf /var/lib/apt/lists/*

ENV CHROME_BIN=/usr/bin/chromium-browser
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
```

#### 3. Environment Variables

```env
# Optional: Custom Chrome binary path
CHROME_BIN=/usr/bin/chromium-browser

# Optional: Skip Chromium download in production
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# Optional: Lighthouse configuration
LIGHTHOUSE_FLAGS=--quiet --chrome-flags="--headless"
```

### API Endpoints

Two sample API endpoints are provided:

1. **`/api/analyze-performance`** - Lighthouse performance analysis
2. **`/api/analyze-seo`** - SEO and technical analysis

Replace the mock implementations with real Puppeteer + Lighthouse code.

## 📊 Core Web Vitals Thresholds

The system uses Google's official Core Web Vitals thresholds:

- **LCP (Largest Contentful Paint)**
  - Good: ≤ 2.5s
  - Needs Improvement: 2.5s - 4.0s  
  - Poor: > 4.0s

- **FID (First Input Delay)**
  - Good: ≤ 100ms
  - Needs Improvement: 100ms - 300ms
  - Poor: > 300ms

- **CLS (Cumulative Layout Shift)**
  - Good: ≤ 0.1
  - Needs Improvement: 0.1 - 0.25
  - Poor: > 0.25

## 🎨 UI Design Matching

The implementation exactly matches the provided Figma design:

- **Tab Navigation**: Clean, underlined active states
- **Performance Overview**: Score display with grade badges
- **Core Web Vitals**: Individual metric cards with status colors
- **Conversion Impact**: Blue highlight box with improvement estimates
- **Advanced Metrics**: Collapsible section with smooth animations
- **Pros and Cons**: Animated entrance with staggered item reveals
- **Color Scheme**: Consistent with existing ConvertIQ branding

## 🔄 Integration Flow

1. **Analysis Creation**: User submits URL for analysis
2. **Background Processing**: 
   - Lighthouse audit runs in headless Chrome
   - SEO scraping extracts page metadata
   - Core Web Vitals are measured
3. **Data Storage**: Results stored in Supabase with proper relationships
4. **UI Display**: Tabbed interface shows all analysis results
5. **Recommendations**: AI-generated suggestions based on performance data

## 🧪 Testing

### Mock Data

The current implementation includes comprehensive mock data for testing:
- Realistic performance scores (60-100 range)
- Varied Core Web Vitals measurements
- Sample SEO issues and recommendations
- Complete Lighthouse report structure

### Real Implementation Testing

When implementing real analysis:

```javascript
// Test Lighthouse integration
const lighthouse = require('lighthouse');
const puppeteer = require('puppeteer');

async function testLighthouse(url) {
  const browser = await puppeteer.launch();
  const { lhr } = await lighthouse(url, {
    port: new URL(browser.wsEndpoint()).port,
    output: 'json',
    onlyCategories: ['performance'],
  });
  
  console.log('Performance Score:', lhr.categories.performance.score * 100);
  await browser.close();
}
```

## 📈 Performance Considerations

- **Lighthouse Analysis**: Can take 30-60 seconds per URL
- **Concurrent Limits**: Limit simultaneous browser instances
- **Memory Usage**: Each Puppeteer instance uses ~100MB RAM
- **Caching**: Consider caching results for recently analyzed URLs
- **Queue System**: Implement job queue for background processing

## 🔒 Security Notes

- **URL Validation**: Always validate and sanitize input URLs
- **Rate Limiting**: Implement rate limits on analysis endpoints
- **Resource Limits**: Set timeouts and memory limits for browser instances
- **Sandboxing**: Run Puppeteer in sandboxed environment
- **CORS**: Configure appropriate CORS policies for API endpoints

## 📝 Next Steps

1. **Execute Database Schema**: Run `performance-schema-updates.sql`
2. **Test UI Components**: Verify tabbed interface and animations
3. **Implement Real Analysis**: Replace mock data with Puppeteer + Lighthouse
4. **Set Up Background Jobs**: Implement queue system for analysis processing
5. **Monitor Performance**: Add logging and monitoring for analysis pipeline
6. **Scale Infrastructure**: Plan for increased server resources

The performance analysis feature is now ready for production implementation with real Lighthouse integration!
