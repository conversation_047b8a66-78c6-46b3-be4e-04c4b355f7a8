import type { AnalysisResult } from '../types/analysis';

const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

if (!OPENROUTER_API_KEY) {
  throw new Error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
}

// --- Model Configuration ---
const models = {
  'qwen': 'qwen/qwen-2.5-72b-instruct:free',
  'claude-sonnet': 'anthropic/claude-3.5-sonnet',
  'gpt-4o': 'openai/gpt-4o',
};

const activeModel = models['qwen'];

// --- End of Model Configuration ---


// Enhanced analysis result interface
export interface EnhancedAnalysisResult extends AnalysisResult {
  performanceInsights: {
    score: number;
    grade: 'A' | 'B' | 'C' | 'D' | 'F';
    conversionImpact: string;
    recommendations: Array<{
      title: string;
      description: string;
      impact: 'High' | 'Medium' | 'Low';
      effort: 'High' | 'Medium' | 'Low';
      category: string;
    }>;
  };
  seoInsights: {
    score: number;
    issues: Array<{
      type: string;
      severity: 'critical' | 'warning' | 'info';
      title: string;
      description: string;
      recommendation: string;
    }>;
    pros: string[];
    cons: string[];
  };
  leadInsights: {
    targetAudience: string;
    qualificationQuestions: string[];
    businessImpact: string[];
    roadmapSuggestions: Array<{
      phase: string;
      title: string;
      description: string;
      timeline: string;
      expectedImpact: string;
    }>;
  };
  overallGrade: 'A' | 'B' | 'C' | 'D' | 'F';
}

// Function to analyze a landing page with comprehensive insights
export async function analyzeLandingPage(html: string, url: string): Promise<AnalysisResult> {
  try {
    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: `You are an expert in landing page optimization, conversion rate optimization, and marketing.
            Analyze the provided landing page HTML and provide a detailed assessment with actionable insights.`
          },
          {
            role: "user",
            content: `Analyze this landing page from ${url}. The HTML content is:
            ${html.substring(0, 15000)} // Truncated to prevent token limit errors

            Provide your analysis in the following JSON format:
            {
              "score": <number 1-10>,
              "conversionRate": <estimated conversion rate as percentage>,
              "pros": [<list of what's good - be specific and detailed>],
              "cons": [<list of what's bad - be specific and detailed>],
              "recommendations": [<actionable recommendations with specific steps>],
              "targetAudience": "<detailed description of likely target audience>",
              "adaptations": [<suggestions for adapting to other audiences>]
            }

            Only respond with valid JSON.`
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 4096,
      })
    });

    const data = await response.json();

    if (data.error) {
      console.error('OpenRouter API Error:', data.error);
      throw new Error(data.error.message || 'An unknown error occurred with the OpenRouter API.');
    }

    if (!data.choices || data.choices.length === 0) {
        console.error('Invalid response from OpenRouter:', data);
        throw new Error('Received an invalid response from the AI.');
    }

    const responseText = data.choices[0].message.content || '{}';
    const analysis = JSON.parse(responseText);

    return {
      score: analysis.score,
      conversionRate: analysis.conversionRate,
      pros: analysis.pros,
      cons: analysis.cons,
      recommendations: analysis.recommendations,
      targetAudience: analysis.targetAudience,
      adaptations: analysis.adaptations
    };
  } catch (error) {
    console.error('Error analyzing landing page:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to analyze landing page');
  }
}

// Function to generate comprehensive analysis with all insights
export async function generateComprehensiveAnalysis(html: string, url: string): Promise<EnhancedAnalysisResult> {
  try {
    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: `You are an expert in landing page optimization, conversion rate optimization, SEO, and digital marketing.
            Analyze the provided landing page HTML and provide a comprehensive assessment covering all aspects of the page.`
          },
          {
            role: "user",
            content: `Analyze this landing page from ${url} comprehensively. The HTML content is:
            ${html.substring(0, 15000)}

            Provide your analysis in the following JSON format:
            {
              "score": <overall score 1-10>,
              "conversionRate": <estimated conversion rate as percentage>,
              "pros": [<detailed list of strengths>],
              "cons": [<detailed list of weaknesses>],
              "recommendations": [<actionable recommendations>],
              "targetAudience": "<detailed target audience description>",
              "adaptations": [<audience adaptation suggestions>],
              "performanceInsights": {
                "score": <performance score 1-100>,
                "grade": "<letter grade A-F>",
                "conversionImpact": "<impact description>",
                "recommendations": [
                  {
                    "title": "<recommendation title>",
                    "description": "<detailed description>",
                    "impact": "<High/Medium/Low>",
                    "effort": "<High/Medium/Low>",
                    "category": "<category name>"
                  }
                ]
              },
              "seoInsights": {
                "score": <SEO score 1-100>,
                "issues": [
                  {
                    "type": "<issue type>",
                    "severity": "<critical/warning/info>",
                    "title": "<issue title>",
                    "description": "<issue description>",
                    "recommendation": "<how to fix>"
                  }
                ],
                "pros": [<SEO strengths>],
                "cons": [<SEO weaknesses>]
              },
              "leadInsights": {
                "targetAudience": "<detailed audience analysis>",
                "qualificationQuestions": [<questions visitors likely have>],
                "businessImpact": [<potential business impacts>],
                "roadmapSuggestions": [
                  {
                    "phase": "<phase name>",
                    "title": "<roadmap item title>",
                    "description": "<detailed description>",
                    "timeline": "<estimated timeline>",
                    "expectedImpact": "<expected impact>"
                  }
                ]
              },
              "overallGrade": "<overall letter grade A-F>"
            }

            Only respond with valid JSON.`
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 6000,
      })
    });

    const data = await response.json();

    if (data.error) {
      console.error('OpenRouter API Error:', data.error);
      throw new Error(data.error.message || 'An unknown error occurred with the OpenRouter API.');
    }

    if (!data.choices || data.choices.length === 0) {
        console.error('Invalid response from OpenRouter:', data);
        throw new Error('Received an invalid response from the AI.');
    }

    const responseText = data.choices[0].message.content || '{}';
    const analysis = JSON.parse(responseText);

    return analysis as EnhancedAnalysisResult;
  } catch (error) {
    console.error('Error generating comprehensive analysis:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to generate comprehensive analysis');
  }
}

// Function to chat about a landing page analysis
export async function chatWithAI(
  messages: { role: string, content: string }[],
  analysisContext: AnalysisResult,
  url: string
): Promise<string> {
  try {
    const systemPrompt = `You are an expert in landing page optimization, conversion rate optimization, and marketing.
    You are having a conversation about the landing page at ${url}.
    
    Here is the analysis data for reference:
    - Score: ${analysisContext.score}/10
    - Estimated conversion rate: ${analysisContext.conversionRate}%
    - Pros: ${JSON.stringify(analysisContext.pros)}
    - Cons: ${JSON.stringify(analysisContext.cons)}
    - Recommendations: ${JSON.stringify(analysisContext.recommendations)}
    - Target audience: ${analysisContext.targetAudience}
    - Adaptations: ${JSON.stringify(analysisContext.adaptations)}
    
    Provide helpful, detailed responses based on this analysis.`;

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          { role: "system", content: systemPrompt },
          ...messages
        ],
        max_tokens: 2048,
      })
    });

    const data = await response.json();

    if (data.error) {
      console.error('OpenRouter API Error:', data.error);
      throw new Error(data.error.message || 'An unknown error occurred with the OpenRouter API.');
    }

    if (!data.choices || data.choices.length === 0) {
        console.error('Invalid response from OpenRouter:', data);
        throw new Error('Received an invalid response from the AI.');
    }

    return data.choices[0].message.content || 'Sorry, I could not generate a response.';
  } catch (error) {
    console.error('Error chatting with AI:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to get AI response');
  }
}