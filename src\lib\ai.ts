import type { AnalysisResult } from '../types/analysis';
import { loggedFetch, log } from './logger';

const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

if (!OPENROUTER_API_KEY) {
  log.error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
  throw new Error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
}

// --- Model Configuration ---
const models = {
  'qwen': 'qwen/qwen-2.5-72b-instruct:free',
  'claude-sonnet': 'anthropic/claude-3.5-sonnet',
  'gpt-4o': 'openai/gpt-4o',
};

const activeModel = models['qwen'];

// --- End of Model Configuration ---


// Enhanced analysis result interface
export interface EnhancedAnalysisResult extends AnalysisResult {
  performanceInsights: {
    score: number;
    grade: 'A' | 'B' | 'C' | 'D' | 'F';
    conversionImpact: string;
    recommendations: Array<{
      title: string;
      description: string;
      impact: 'High' | 'Medium' | 'Low';
      effort: 'High' | 'Medium' | 'Low';
      category: string;
    }>;
  };
  seoInsights: {
    score: number;
    issues: Array<{
      type: string;
      severity: 'critical' | 'warning' | 'info';
      title: string;
      description: string;
      recommendation: string;
    }>;
    pros: string[];
    cons: string[];
  };
  leadInsights: {
    targetAudience: string;
    qualificationQuestions: string[];
    businessImpact: string[];
    roadmapSuggestions: Array<{
      phase: string;
      title: string;
      description: string;
      timeline: string;
      expectedImpact: string;
    }>;
  };
  overallGrade: 'A' | 'B' | 'C' | 'D' | 'F';
}

// Function to analyze a landing page with comprehensive insights
export async function analyzeLandingPage(html: string, url: string): Promise<AnalysisResult> {
  log.info(`Analyzing landing page for URL: ${url}`);
  try {
    log.debug('Sending request to OpenRouter for basic analysis...');
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: `You are an expert in landing page optimization, conversion rate optimization, and marketing.\n            Analyze the provided landing page HTML and provide a detailed assessment with actionable insights.`
          },
          {
            role: "user",
            content: `Analyze this landing page from ${url}. The HTML content is:\n            ${html.substring(0, 15000)} // Truncated to prevent token limit errors\n\n            Provide your analysis in the following JSON format:\n            {\n              "score": <number 1-10>,\n              "conversionRate": <estimated conversion rate as percentage>,\n              "pros": [<list of what's good - be specific and detailed>],\n              "cons": [<list of what's bad - be specific and detailed>],\n              "recommendations": [<actionable recommendations with specific steps>],\n              "targetAudience": "<detailed description of likely target audience>",\n              "adaptations": [<suggestions for adapting to other audiences>]\n            }\n\n            Only respond with valid JSON.`
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 4096,
      })
    });

    const data = await response.json();

    if (data.error) {
      log.error(`OpenRouter API Error during basic analysis: ${data.error.message || JSON.stringify(data.error)}`);
      throw new Error(data.error.message || 'An unknown error occurred with the OpenRouter API.');
    }

    if (!data.choices || data.choices.length === 0) {
        log.error(`Invalid response from OpenRouter during basic analysis: ${JSON.stringify(data)}`);
        throw new Error('Received an invalid response from the AI.');
    }

    const responseText = data.choices[0].message.content || '{}';
    log.debug(`Raw AI response for basic analysis: ${responseText.substring(0, 500)}...`);
    const analysis = JSON.parse(responseText);
    log.info(`Basic analysis completed for ${url}. Score: ${analysis.score}`);

    return {
      score: analysis.score,
      conversionRate: analysis.conversionRate,
      pros: analysis.pros,
      cons: analysis.cons,
      recommendations: analysis.recommendations,
      targetAudience: analysis.targetAudience,
      adaptations: analysis.adaptations
    };
  } catch (error) {
    log.error(`Error analyzing landing page: ${error instanceof Error ? error.message : error}`);
    throw new Error(error instanceof Error ? error.message : 'Failed to analyze landing page');
  }
}

// Function to generate comprehensive analysis with all insights
export async function generateComprehensiveAnalysis(html: string, url: string): Promise<EnhancedAnalysisResult> {
  log.info(`Generating comprehensive analysis for URL: ${url}`);
  try {
    log.debug('Sending request to OpenRouter for comprehensive analysis...');
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: `You are an expert in landing page optimization, conversion rate optimization, SEO, and digital marketing.
            Analyze the provided landing page HTML and provide a comprehensive assessment covering all aspects of the page.`
          },
          {
            role: "user",
            content: `Analyze this landing page from ${url} comprehensively. The HTML content is:
            ${html.substring(0, 15000)}

            Provide your analysis in the following JSON format:
            {
              "score": <overall score 1-10>,
              "conversionRate": <estimated conversion rate as percentage>,
              "pros": [<detailed list of strengths>],
              "cons": [<detailed list of weaknesses>],
              "recommendations": [<actionable recommendations>],
              "targetAudience": "<detailed target audience description>",
              "adaptations": [<audience adaptation suggestions>],
              "performanceInsights": {
                "score": <performance score 1-100>,
                "grade": "<letter grade A-F>",
                "conversionImpact": "<impact description>",
                "recommendations": [
                  {
                    "title": "<recommendation title>",
                    "description": "<detailed description>",
                    "impact": "<High/Medium/Low>",
                    "effort": "<High/Medium/Low>",
                    "category": "<category name>"
                  }
                ]
              },
              "seoInsights": {
                "score": <SEO score 1-100>,
                "issues": [
                  {
                    "type": "<issue type>",
                    "severity": "<critical/warning/info>",
                    "title": "<issue title>",
                    "description": "<issue description>",
                    "recommendation": "<how to fix>"
                  }
                ],
                "pros": [<SEO strengths>],
                "cons": [<SEO weaknesses>]
              },
              "leadInsights": {
                "targetAudience": "<detailed audience analysis with demographics, pain points, and behavior patterns>",
                "qualificationQuestions": [
                  "<specific questions potential customers likely have about the product/service>",
                  "<questions about pricing, features, or implementation>",
                  "<questions about company credibility and experience>",
                  "<questions about support and onboarding>",
                  "<questions about ROI and business value>"
                ],
                "businessImpact": [
                  "<positive business impacts and opportunities>",
                  "<potential revenue improvements>",
                  "<competitive advantages>",
                  "<market positioning benefits>"
                ],
                "roadmapSuggestions": [
                  {
                    "phase": "Immediate (0-2 weeks)",
                    "title": "<quick win optimization>",
                    "description": "<detailed implementation steps>",
                    "timeline": "1-2 weeks",
                    "expectedImpact": "<specific measurable impact>"
                  },
                  {
                    "phase": "Short-term (1-3 months)",
                    "title": "<medium effort improvement>",
                    "description": "<detailed implementation steps>",
                    "timeline": "4-12 weeks",
                    "expectedImpact": "<specific measurable impact>"
                  },
                  {
                    "phase": "Long-term (3-6 months)",
                    "title": "<strategic enhancement>",
                    "description": "<detailed implementation steps>",
                    "timeline": "3-6 months",
                    "expectedImpact": "<specific measurable impact>"
                  }
                ]
              },
              "overallGrade": "<overall letter grade A-F>"
            }

            Only respond with valid JSON.`
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 6000,
      })
    });

    const data = await response.json();

    if (data.error) {
      log.error(`OpenRouter API Error during comprehensive analysis: ${data.error.message || JSON.stringify(data.error)}`);
      throw new Error(data.error.message || 'An unknown error occurred with the OpenRouter API.');
    }

    if (!data.choices || data.choices.length === 0) {
        log.error(`Invalid response from OpenRouter during comprehensive analysis: ${JSON.stringify(data)}`);
        throw new Error('Received an invalid response from the AI.');
    }

    const responseText = data.choices[0].message.content || '{}';
    log.debug(`Raw AI response for comprehensive analysis: ${responseText.substring(0, 500)}...`);
    const analysis = JSON.parse(responseText);
    log.info(`Comprehensive analysis completed for ${url}. Overall Grade: ${analysis.overallGrade}`);

    return analysis as EnhancedAnalysisResult;
  } catch (error) {
    log.error(`Error generating comprehensive analysis: ${error instanceof Error ? error.message : error}`);
    throw new Error(error instanceof Error ? error.message : 'Failed to generate comprehensive analysis');
  }
}

// Function to chat about a landing page analysis
export async function chatWithAI(
  messages: { role: string, content: string }[],
  analysisContext: AnalysisResult,
  url: string
): Promise<string> {
  log.info(`Initiating AI chat for URL: ${url}`);
  try {
    const systemPrompt = `You are an expert in landing page optimization, conversion rate optimization, and marketing.
    You are having a conversation about the landing page at ${url}.
    
    Here is the analysis data for reference:
    - Score: ${analysisContext.score}/10
    - Estimated conversion rate: ${analysisContext.conversionRate}%
    - Pros: ${JSON.stringify(analysisContext.pros)}
    - Cons: ${JSON.stringify(analysisContext.cons)}
    - Recommendations: ${JSON.stringify(analysisContext.recommendations)}
    - Target audience: ${analysisContext.targetAudience}
    - Adaptations: ${JSON.stringify(analysisContext.adaptations)}
    
    Provide helpful, detailed responses based on this analysis.`;

    log.debug('Sending request to OpenRouter for chat completion...');
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          { role: "system", content: systemPrompt },
          ...messages
        ],
        max_tokens: 2048,
      })
    });

    const data = await response.json();

    if (data.error) {
      log.error(`OpenRouter API Error during chat: ${data.error.message || JSON.stringify(data.error)}`);
      throw new Error(data.error.message || 'An unknown error occurred with the OpenRouter API.');
    }

    if (!data.choices || data.choices.length === 0) {
        log.error(`Invalid response from OpenRouter during chat: ${JSON.stringify(data)}`);
        throw new Error('Received an invalid response from the AI.');
    }

    const responseText = data.choices[0].message.content || 'Sorry, I could not generate a response.';
    log.debug(`Raw AI response for chat: ${responseText.substring(0, 500)}...`);
    log.info(`AI chat response received for ${url}`);

    return responseText;
  } catch (error) {
    log.error(`Error chatting with AI: ${error instanceof Error ? error.message : error}`);
    throw new Error(error instanceof Error ? error.message : 'Failed to get AI response');
  }
}