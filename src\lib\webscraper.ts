export interface ScrapedWebsite {
  url: string;
  title: string;
  content: string;
  headings: string[];
  images: string[];
  links: string[];
  metadata: {
    description?: string;
    keywords?: string;
    author?: string;
  };
}

import { loggedFetch, log } from './logger';

export async function scrapeWebsite(url: string): Promise<ScrapedWebsite> {
  try {
    // Validate URL
    const urlObj = new URL(url);
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      log.error(`Invalid URL protocol for ${url}`);
      throw new Error('Invalid URL protocol. Only HTTP and HTTPS are supported.');
    }

    // Use our API endpoint to fetch the website content
    log.info(`Initiating scrape for URL: ${url}`);
    const response = await loggedFetch(`/api/scrape-website?url=${encodeURIComponent(url)}`);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Failed to fetch website: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error scraping website:', error);
    if (error instanceof TypeError && error.message.includes('Invalid URL')) {
      throw new Error('Please enter a valid URL (e.g., https://example.com)');
    }
    throw error;
  }
}

export function validateUrl(url: string): { isValid: boolean; error?: string } {
  if (!url.trim()) {
    return { isValid: false, error: 'Please enter a URL' };
  }

  try {
    const urlObj = new URL(url);
    
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return { isValid: false, error: 'URL must use HTTP or HTTPS protocol' };
    }
    
    if (!urlObj.hostname) {
      return { isValid: false, error: 'Invalid hostname' };
    }
    
    // Check for localhost or private IPs
    if (urlObj.hostname === 'localhost' || 
        urlObj.hostname.startsWith('127.') || 
        urlObj.hostname.startsWith('192.168.') ||
        urlObj.hostname.startsWith('10.') ||
        urlObj.hostname.includes('internal')) {
      return { isValid: false, error: 'Cannot analyze local or private URLs' };
    }
    
    return { isValid: true };
  } catch (e) {
    return { isValid: false, error: 'Please enter a valid URL (e.g., https://example.com)' };
  }
}

export function normalizeUrl(url: string): string {
  if (!url.match(/^https?:\/\//)) {
    url = 'https://' + url;
  }
  
  try {
    const urlObj = new URL(url);
    return urlObj.toString();
  } catch (e) {
    return url;
  }
}