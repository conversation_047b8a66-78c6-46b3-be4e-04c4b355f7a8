<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { ChevronLeft, ChevronRight } from 'lucide-vue-next';

const props = defineProps<{
  tabs: { id: string; name: string; icon: any }[];
  activeTab: string;
}>();

const emit = defineEmits(['update:activeTab']);

const tabsContainer = ref<HTMLElement | null>(null);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);

const handleScroll = () => {
  if (!tabsContainer.value) return;
  const { scrollLeft, scrollWidth, clientWidth } = tabsContainer.value;
  // Use a small tolerance to prevent floating point inaccuracies
  const tolerance = 1;
  showLeftArrow.value = scrollLeft > tolerance;
  showRightArrow.value = scrollLeft < scrollWidth - clientWidth - tolerance;
};

const scroll = (direction: 'left' | 'right') => {
  if (!tabsContainer.value) return;
  const scrollAmount = tabsContainer.value.clientWidth * 0.8;
  tabsContainer.value.scrollBy({
    left: direction === 'left' ? -scrollAmount : scrollAmount,
    behavior: 'smooth',
  });
};

const scrollToActiveTab = () => {
  if (!tabsContainer.value) return;
  const activeTabElement = tabsContainer.value.querySelector('.active-tab');
  if (activeTabElement) {
    activeTabElement.scrollIntoView({
      behavior: 'smooth',
      inline: 'center',
      block: 'nearest',
    });
  }
};

const checkArrows = () => {
  // nextTick ensures that the DOM has been updated after a render
  nextTick(() => {
    handleScroll();
  });
};

onMounted(() => {
  tabsContainer.value?.addEventListener('scroll', handleScroll, { passive: true });
  window.addEventListener('resize', checkArrows);
  checkArrows();
  // A short delay before scrolling to active tab can help if content loads slowly
  setTimeout(() => {
    scrollToActiveTab();
  }, 100);
});

onBeforeUnmount(() => {
  tabsContainer.value?.removeEventListener('scroll', handleScroll);
  window.removeEventListener('resize', checkArrows);
});

watch(() => props.activeTab, () => {
    // Ensure the scroll to active tab happens after the DOM is updated
    nextTick(() => {
        scrollToActiveTab();
    });
});

// Also watch the tabs array itself in case it changes
watch(() => props.tabs, checkArrows, { deep: true });

</script>

<template>
  <div class="relative flex items-center p-2">
    <!-- Left Arrow -->
    <div
      class="absolute left-2 top-0 bottom-0 z-20 flex items-center justify-center transition-opacity duration-300"
      :class="{ 'opacity-0 pointer-events-none': !showLeftArrow }"
    >
      <button
        @click="scroll('left')"
        class="bg-white/80 backdrop-blur-sm rounded-full shadow-md w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900"
      >
        <ChevronLeft class="w-5 h-5" />
      </button>
    </div>

    <!-- Scrolling Container -->
    <div
      ref="tabsContainer"
      @scroll="handleScroll"
      class="flex items-center space-x-8 px-10 overflow-x-auto scrollbar-hide"
    >
      <button
        v-for="tab in tabs"
        :key="tab.id"
        @click="$emit('update:activeTab', tab.id)"
        class="flex-shrink-0 flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-all duration-300 transform hover:scale-105 whitespace-nowrap"
        :class="[
          activeTab === tab.id
            ? 'border-blue-500 text-blue-600 active-tab'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
        ]"
      >
        <component :is="tab.icon" class="w-4 h-4 mr-2" />
        {{ tab.name }}
      </button>
    </div>

    <!-- Right Arrow -->
    <div
      class="absolute right-2 top-0 bottom-0 z-20 flex items-center justify-center transition-opacity duration-300"
      :class="{ 'opacity-0 pointer-events-none': !showRightArrow }"
    >
      <button
        @click="scroll('right')"
        class="bg-white/80 backdrop-blur-sm rounded-full shadow-md w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900"
      >
        <ChevronRight class="w-5 h-5" />
      </button>
    </div>

    <!-- Fades -->
    <div
      class="absolute top-0 bottom-0 left-0 w-12 bg-gradient-to-r from-white to-transparent pointer-events-none transition-opacity duration-300"
      :class="{ 'opacity-0': !showLeftArrow }"
    ></div>
    <div
      class="absolute top-0 bottom-0 right-0 w-12 bg-gradient-to-l from-white to-transparent pointer-events-none transition-opacity duration-300"
      :class="{ 'opacity-0': !showRightArrow }"
    ></div>
  </div>
</template>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>