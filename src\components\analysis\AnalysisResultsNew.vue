<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/supabase';
import PerformanceTab from './PerformanceTab.vue';
import SEOAnalysisTab from './SEOAnalysisTab.vue';
import ProsConsTab from './ProsConsTab.vue';
import LeadInsightsTab from './LeadInsightsTab.vue';
import {
  ArrowLeft,
  MessageCircle,
  Eye,
  Lightbulb,
  Zap,
  Search,
  BarChart3,
  Users,
  Camera,
  TrendingUp,
  AlertTriangle
} from 'lucide-vue-next';

const props = defineProps<{
  analysisId: string;
}>();

type Analysis = Database['public']['Tables']['analyses']['Row'];
type Suggestion = Database['public']['Tables']['suggestions']['Row'];

const analysis = ref<Analysis | null>(null);
const suggestions = ref<Suggestion[]>([]);
const loading = ref(true);
const error = ref('');
const activeTab = ref('suggestions');

onMounted(async () => {
  await loadAnalysisData();
});

const loadAnalysisData = async () => {
  try {
    loading.value = true;
    error.value = '';

    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      throw new Error('User not authenticated');
    }

    // Fetch analysis data
    const { data: analysisData, error: analysisError } = await supabase
      .from('analyses')
      .select('*')
      .eq('id', props.analysisId)
      .eq('user_id', userData.user.id)
      .single();

    if (analysisError) {
      throw new Error('Analysis not found or access denied');
    }

    analysis.value = analysisData;

    // Fetch suggestions
    const { data: suggestionsData, error: suggestionsError } = await supabase
      .from('suggestions')
      .select('*')
      .eq('analysis_id', props.analysisId)
      .order('priority', { ascending: true });

    if (suggestionsError) {
      console.error('Error loading suggestions:', suggestionsError);
    } else {
      suggestions.value = suggestionsData || [];
    }

  } catch (e) {
    console.error('Error loading analysis:', e);
    error.value = e instanceof Error ? e.message : 'Failed to load analysis';
  } finally {
    loading.value = false;
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: 'numeric'
  }).format(date);
};

const getScoreColor = (score: number) => {
  if (score >= 8) return 'text-green-600';
  if (score >= 6) return 'text-yellow-600';
  return 'text-red-600';
};

const getScoreProgress = (score: number) => {
  return (score / 10) * 100;
};

const getImpactColor = (impact: string) => {
  switch (impact) {
    case 'High': return 'bg-red-100 text-red-800';
    case 'Medium': return 'bg-yellow-100 text-yellow-800';
    case 'Low': return 'bg-green-100 text-green-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getEffortColor = (effort: string) => {
  switch (effort) {
    case 'High': return 'text-red-600';
    case 'Medium': return 'text-yellow-600';
    case 'Low': return 'text-green-600';
    default: return 'text-gray-600';
  }
};

const groupedSuggestions = computed(() => {
  const groups: Record<string, Suggestion[]> = {};
  suggestions.value.forEach(suggestion => {
    if (!groups[suggestion.category]) {
      groups[suggestion.category] = [];
    }
    groups[suggestion.category].push(suggestion);
  });
  return groups;
});

const getOverallGrade = computed(() => {
  if (!analysis.value) return { grade: 'N/A', color: 'text-gray-600', bg: 'bg-gray-50' };

  const scores = [
    analysis.value.score || 0,
    analysis.value.performance_score || 0,
    analysis.value.seo_score || 0
  ].filter(score => score > 0);

  if (scores.length === 0) return { grade: 'N/A', color: 'text-gray-600', bg: 'bg-gray-50' };

  const overallScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);

  if (overallScore >= 90) return { grade: 'A', color: 'text-green-600', bg: 'bg-green-50' };
  if (overallScore >= 80) return { grade: 'B', color: 'text-blue-600', bg: 'bg-blue-50' };
  if (overallScore >= 70) return { grade: 'C', color: 'text-yellow-600', bg: 'bg-yellow-50' };
  if (overallScore >= 60) return { grade: 'D', color: 'text-orange-600', bg: 'bg-orange-50' };
  return { grade: 'F', color: 'text-red-600', bg: 'bg-red-50' };
});

const tabs = [
  { id: 'suggestions', name: 'Suggestions', icon: Lightbulb },
  { id: 'performance', name: 'Performance', icon: Zap },
  { id: 'seo', name: 'SEO Insights', icon: Search },
  { id: 'proscons', name: 'Pros & Cons', icon: BarChart3 },
  { id: 'leads', name: 'Lead Insights', icon: Users }
];

const setActiveTab = (tabId: string) => {
  activeTab.value = tabId;
};
</script>

<template>
  <div class="min-h-screen bg-gray-50 mb">

    <!-- Loading State -->
    <div v-if="loading" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="animate-pulse">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div class="h-32 bg-gray-200 rounded mb-6"></div>
              <div class="space-y-3">
                <div class="h-4 bg-gray-200 rounded"></div>
                <div class="h-4 bg-gray-200 rounded w-5/6"></div>
              </div>
            </div>
          </div>
          <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
              <div class="space-y-4">
                <div class="h-24 bg-gray-200 rounded"></div>
                <div class="h-24 bg-gray-200 rounded"></div>
                <div class="h-24 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-red-50 border border-red-200 rounded-lg p-6">
        <div class="flex">
          <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h3 class="text-red-800 font-medium">Error Loading Analysis</h3>
            <p class="text-red-700 mt-1">{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-else-if="analysis" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Sidebar - Overview -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow overflow-hidden">
            <!-- Screenshot at the very top - no padding/margin -->
            <div class="w-full">
              <div v-if="analysis.screenshot_url" class="w-full aspect-video">
                <img
                  :src="analysis.screenshot_url"
                  :alt="`Screenshot of ${analysis.url}`"
                  class="w-full h-full object-cover"
                />
              </div>
              <div v-else class="w-full aspect-video bg-gray-100 flex items-center justify-center">
                <div class="text-center text-gray-400">
                  <Camera class="w-12 h-12 mx-auto mb-2" />
                  <p class="text-sm">Screenshot not available</p>
                </div>
              </div>
            </div>

            <!-- Overview Header -->
            <div class="p-6 border-b border-gray-200">
              <div class="flex items-center mb-2">
                <Eye class="w-5 h-5 text-gray-400 mr-2" />
                <h2 class="text-lg font-semibold text-gray-900">Overview</h2>
              </div>
            </div>

            <!-- Page Info -->
            <div class="p-6 border-b border-gray-200">
              <h3 class="font-medium text-gray-900 mb-1">{{ analysis.title }}</h3>
              <a :href="analysis.url" target="_blank" class="text-sm text-blue-600 hover:text-blue-800 break-all">
                {{ analysis.url }}
              </a>
            </div>

            <!-- Conversion Score with Grade -->
            <div class="p-6 border-b border-gray-200">
              <div class="flex items-center justify-between mb-4">
                <h4 class="font-medium text-gray-900">Conversion Score</h4>
                <div
                  class="w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold"
                  :class="getOverallGrade.bg + ' ' + getOverallGrade.color"
                >
                  {{ getOverallGrade.grade }}
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="text-3xl font-bold" :class="getScoreColor(analysis.score)">
                  {{ analysis.score || 0 }}
                </span>
                <span class="text-gray-500 ml-1">/100</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div
                  class="h-2 rounded-full transition-all duration-500"
                  :class="(analysis.score || 0) >= 80 ? 'bg-green-500' : (analysis.score || 0) >= 60 ? 'bg-yellow-500' : 'bg-red-500'"
                  :style="{ width: `${(analysis.score || 0)}%` }"
                ></div>
              </div>
            </div>

            <!-- Stats -->
            <div class="p-6">
              <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-3 bg-blue-50 rounded-lg transition-all duration-300 hover:bg-blue-100">
                  <TrendingUp class="w-6 h-6 text-blue-600 mx-auto mb-2" />
                  <div class="text-2xl font-bold text-gray-900">{{ analysis.suggestions_count || 0 }}</div>
                  <div class="text-sm text-gray-500">Suggestions</div>
                </div>
                <div class="text-center p-3 bg-red-50 rounded-lg transition-all duration-300 hover:bg-red-100">
                  <AlertTriangle class="w-6 h-6 text-red-600 mx-auto mb-2" />
                  <div class="text-2xl font-bold text-red-600">{{ analysis.priority_issues_count || 0 }}</div>
                  <div class="text-sm text-gray-500">Priority Issues</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Content - Tabbed Interface -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-lg shadow">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200">
              <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button
                  v-for="tab in tabs"
                  :key="tab.id"
                  @click="setActiveTab(tab.id)"
                  class="flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-all duration-300 transform hover:scale-105"
                  :class="activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                >
                  <component :is="tab.icon" class="w-4 h-4 mr-2" />
                  {{ tab.name }}
                </button>
              </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-6">
              <!-- Suggestions Tab -->
              <div v-if="activeTab === 'suggestions'" class="transition-all duration-300 ease-in-out">
                <div class="flex items-center mb-4">
                  <Lightbulb class="w-5 h-5 text-gray-400 mr-2" />
                  <h2 class="text-lg font-semibold text-gray-900">AI Recommendations</h2>
                </div>
                <p class="text-gray-600 mb-6">Actionable insights to improve your conversion rate</p>

                <!-- Suggestions List -->
                <div v-if="suggestions.length === 0" class="text-center py-12">
                  <Lightbulb class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 mb-2">No suggestions yet</h3>
                  <p class="text-gray-500">Analysis is still processing or no suggestions were generated.</p>
                </div>

                <div v-else class="space-y-6">
                  <div v-for="(categoryGroup, category) in groupedSuggestions" :key="category" class="space-y-4">
                    <div v-for="suggestion in categoryGroup" :key="suggestion.id" class="border border-gray-200 rounded-lg p-6">
                      <!-- Suggestion Header -->
                      <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                          <div class="flex items-center mb-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-3">
                              {{ suggestion.category }}
                            </span>
                            <span
                              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              :class="getImpactColor(suggestion.impact_level)"
                            >
                              {{ suggestion.impact_level }}
                            </span>
                          </div>
                          <h3 class="text-lg font-medium text-gray-900">{{ suggestion.title }}</h3>
                        </div>
                        <div class="text-right text-sm">
                          <div class="text-gray-500">Impact: {{ suggestion.impact_level }}</div>
                          <div :class="getEffortColor(suggestion.effort_level)">Effort: {{ suggestion.effort_level }}</div>
                        </div>
                      </div>

                      <!-- Suggestion Content -->
                      <p class="text-gray-700 mb-4">{{ suggestion.description }}</p>

                      <div v-if="suggestion.detailed_explanation" class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700 text-sm">{{ suggestion.detailed_explanation }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Performance Tab -->
              <div v-else-if="activeTab === 'performance'" class="transition-all duration-300 ease-in-out">
                <PerformanceTab :analysis="analysis" />
              </div>

              <!-- SEO Analysis Tab -->
              <div v-else-if="activeTab === 'seo'" class="transition-all duration-300 ease-in-out">
                <SEOAnalysisTab :analysis="analysis" />
              </div>

              <!-- Pros & Cons Tab -->
              <div v-else-if="activeTab === 'proscons'" class="transition-all duration-300 ease-in-out">
                <ProsConsTab :analysis="analysis" />
              </div>

              <!-- Lead Insights Tab -->
              <div v-else-if="activeTab === 'leads'" class="transition-all duration-300 ease-in-out">
                <LeadInsightsTab :analysis="analysis" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>