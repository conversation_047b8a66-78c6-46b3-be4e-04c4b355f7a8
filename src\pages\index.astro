---
import Layout from '../layouts/Layout.astro';
import WebsiteAnalyzer from '../components/WebsiteAnalyzer.vue';
import { Search, FileText, Users, Settings } from 'lucide-vue-next';
---

<Layout title="Website Analysis Platform - AI-Powered Website Insights">
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-bold text-gray-900">Website Analyzer</h1>
          </div>
          <nav class="flex items-center space-x-4">
            <a href="/dashboard" class="text-gray-700 hover:text-blue-600 font-medium">Dashboard</a>
            <a href="/about" class="text-gray-700 hover:text-blue-600 font-medium">About</a>
            <a href="/pricing" class="text-gray-700 hover:text-blue-600 font-medium">Pricing</a>
            <a href="/login" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">Sign In</a>
          </nav>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="py-12">
      <WebsiteAnalyzer client:load />
    </main>

    <!-- Features Section -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Comprehensive Website Analysis</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Get detailed insights about any website with our AI-powered analysis platform
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="text-center group">
            <div class="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center transition-all duration-300 group-hover:bg-blue-200 group-hover:scale-110">
              <Search class="h-8 w-8 text-blue-600" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">SEO Analysis</h3>
            <p class="text-gray-600">Comprehensive SEO evaluation with actionable recommendations</p>
          </div>

          <div class="text-center group">
            <div class="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center transition-all duration-300 group-hover:bg-green-200 group-hover:scale-110">
              <FileText class="h-8 w-8 text-green-600" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Content Quality</h3>
            <p class="text-gray-600">Analyze content structure, readability, and engagement</p>
          </div>

          <div class="text-center group">
            <div class="bg-purple-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center transition-all duration-300 group-hover:bg-purple-200 group-hover:scale-110">
              <Users class="h-8 w-8 text-purple-600" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">User Experience</h3>
            <p class="text-gray-600">Evaluate usability, accessibility, and user journey</p>
          </div>

          <div class="text-center group">
            <div class="bg-orange-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center transition-all duration-300 group-hover:bg-orange-200 group-hover:scale-110">
              <Settings class="h-8 w-8 text-orange-600" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Technical Performance</h3>
            <p class="text-gray-600">Check technical implementation and performance metrics</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</Layout>