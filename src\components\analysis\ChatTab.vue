<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue';
import { Send, Sparkles, MessageCircle, Lightbulb, TrendingUp, Search, BarChart3 } from 'lucide-vue-next';
import type { Database } from '../../types/supabase';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
}

const messages = ref<ChatMessage[]>([]);
const newMessage = ref('');
const loading = ref(false);
const chatContainer = ref<HTMLElement>();

// Chat prompt suggestions - configurable array
const chatSuggestions = ref([
  {
    icon: TrendingUp,
    title: 'Performance Optimization',
    prompt: 'How can I improve my website\'s loading speed and Core Web Vitals?'
  },
  {
    icon: Search,
    title: 'SEO Improvements',
    prompt: 'What are the most important SEO changes I should make first?'
  },
  {
    icon: BarChart3,
    title: 'Conversion Rate',
    prompt: 'What specific changes could increase my conversion rate?'
  },
  {
    icon: Lightbulb,
    title: 'Quick Wins',
    prompt: 'What are some quick, easy improvements I can implement today?'
  },
  {
    icon: MessageCircle,
    title: 'User Experience',
    prompt: 'How can I improve the user experience on my landing page?'
  },
  {
    icon: Sparkles,
    title: 'Lead Generation',
    prompt: 'What strategies can help me generate more qualified leads?'
  }
]);

// Get 3-4 random suggestions to display
const displayedSuggestions = computed(() => {
  const shuffled = [...chatSuggestions.value].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, 4);
});

const sendMessage = async (content?: string) => {
  const messageContent = content || newMessage.value.trim();
  if (!messageContent || loading.value) return;

  // Add user message
  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    content: messageContent,
    role: 'user',
    timestamp: new Date()
  };
  
  messages.value.push(userMessage);
  newMessage.value = '';
  loading.value = true;

  // Scroll to bottom
  await nextTick();
  scrollToBottom();

  try {
    // Send to chat API
    const response = await fetch('/api/chat-analysis', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: messageContent,
        analysisId: props.analysis.id,
        analysisData: {
          url: props.analysis.url,
          score: props.analysis.score,
          pros: props.analysis.pros,
          cons: props.analysis.cons,
          recommendations: props.analysis.recommendations,
          targetAudience: props.analysis.target_audience,
          performanceScore: props.analysis.performance_score,
          seoScore: props.analysis.seo_score
        }
      })
    });

    if (!response.ok) {
      throw new Error('Failed to get response');
    }

    const data = await response.json();
    
    // Add assistant message
    const assistantMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      content: data.response,
      role: 'assistant',
      timestamp: new Date()
    };
    
    messages.value.push(assistantMessage);
    
    // Scroll to bottom
    await nextTick();
    scrollToBottom();
    
  } catch (error) {
    console.error('Error sending message:', error);
    
    // Add error message
    const errorMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      content: 'Sorry, I encountered an error. Please try again.',
      role: 'assistant',
      timestamp: new Date()
    };
    
    messages.value.push(errorMessage);
  } finally {
    loading.value = false;
  }
};

const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
};

const useSuggestion = (suggestion: any) => {
  sendMessage(suggestion.prompt);
};

onMounted(() => {
  // Add welcome message
  const welcomeMessage: ChatMessage = {
    id: 'welcome',
    content: `Hi! I'm here to help you understand your website analysis and answer any questions about improving your site. I have context about your website at ${props.analysis.url} and can provide specific recommendations based on the analysis results.`,
    role: 'assistant',
    timestamp: new Date()
  };
  
  messages.value.push(welcomeMessage);
});
</script>

<template>
  <div class="flex flex-col h-full max-h-[600px]">
    <!-- Header -->
    <div class="flex items-center mb-6">
      <Sparkles class="w-5 h-5 text-gray-400 mr-2" />
      <h3 class="text-lg font-semibold text-gray-900">AI Assistant</h3>
    </div>

    <!-- Chat Messages Container -->
    <div 
      ref="chatContainer"
      class="flex-1 overflow-y-auto space-y-4 mb-4 p-4 bg-gray-50 rounded-lg border"
      style="min-height: 300px; max-height: 400px;"
    >
      <div
        v-for="message in messages"
        :key="message.id"
        class="flex"
        :class="message.role === 'user' ? 'justify-end' : 'justify-start'"
      >
        <div
          class="max-w-[80%] p-3 rounded-lg transition-all duration-300 ease-in-out"
          :class="message.role === 'user' 
            ? 'bg-blue-600 text-white rounded-br-sm' 
            : 'bg-white text-gray-900 border border-gray-200 rounded-bl-sm shadow-sm'"
        >
          <p class="text-sm leading-relaxed">{{ message.content }}</p>
          <div class="text-xs mt-2 opacity-70">
            {{ message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) }}
          </div>
        </div>
      </div>

      <!-- Loading indicator -->
      <div v-if="loading" class="flex justify-start">
        <div class="bg-white text-gray-900 border border-gray-200 rounded-lg rounded-bl-sm shadow-sm p-3">
          <div class="flex items-center space-x-2">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
            <span class="text-sm text-gray-500">AI is thinking...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Suggestions (shown when no messages or at start) -->
    <div v-if="messages.length <= 1" class="mb-4">
      <p class="text-sm text-gray-600 mb-3">Choose a suggestion below or ask me anything about your website:</p>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
        <button
          v-for="suggestion in displayedSuggestions"
          :key="suggestion.title"
          @click="useSuggestion(suggestion)"
          class="flex items-start p-3 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
        >
          <component :is="suggestion.icon" class="w-4 h-4 text-gray-400 group-hover:text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
          <div>
            <div class="font-medium text-sm text-gray-900 mb-1">{{ suggestion.title }}</div>
            <div class="text-xs text-gray-600">{{ suggestion.prompt }}</div>
          </div>
        </button>
      </div>
    </div>

    <!-- Input Area -->
    <div class="flex items-end space-x-2">
      <div class="flex-1">
        <textarea
          v-model="newMessage"
          @keydown.enter.prevent="sendMessage()"
          placeholder="Ask me anything about your website analysis..."
          class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          rows="2"
          :disabled="loading"
        ></textarea>
      </div>
      <button
        @click="sendMessage()"
        :disabled="!newMessage.trim() || loading"
        class="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex-shrink-0"
      >
        <Send class="w-4 h-4" />
      </button>
    </div>

    <!-- Disclaimer -->
    <p class="text-xs text-gray-500 mt-2 text-center">
      AI can make mistakes. Consider checking important information.
    </p>
  </div>
</template>
