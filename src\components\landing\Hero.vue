<script setup lang="ts">
import { ref } from 'vue';

const isAnimating = ref(true);

// Stop animation after it completes
setTimeout(() => {
  isAnimating.value = false;
}, 3000);
</script>

<template>
  <section class="relative pt-20 pb-16 overflow-hidden bg-gradient-to-r from-primary-500/5 to-accent-500/5">
    <div class="container mx-auto px-6 py-16 md:py-24">
      <div class="md:flex md:items-center md:justify-between">
        <div class="md:w-1/2 md:pr-12">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
            <span class="block mb-2">AI-Powered</span>
            <span class="text-primary-500">Landing Page Analysis</span>
          </h1>
          <p class="mt-4 text-xl text-gray-600 max-w-2xl">
            ConvertIQ uses advanced AI to analyze your landing pages, score performance, and provide actionable recommendations to increase conversions.
          </p>
          <div class="mt-8 flex flex-wrap gap-4">
            <a 
              href="/login" 
              class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-500 hover:bg-primary-600 transition-colors md:py-4 md:text-lg md:px-8"
            >
              Get Started
            </a>
            <a 
              href="#how-it-works" 
              class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors md:py-4 md:text-lg md:px-8"
            >
              Learn More
            </a>
          </div>
        </div>
        
        <div class="mt-12 md:mt-0 md:w-1/2 relative">
          <div class="bg-white rounded-lg shadow-xl overflow-hidden border border-gray-200">
            <div class="p-4 bg-gray-50 border-b border-gray-200">
              <div class="flex items-center">
                <div class="flex space-x-1.5">
                  <div class="w-3 h-3 rounded-full bg-error-500"></div>
                  <div class="w-3 h-3 rounded-full bg-warning-500"></div>
                  <div class="w-3 h-3 rounded-full bg-success-500"></div>
                </div>
                <div class="mx-auto text-sm text-gray-500 font-medium">Landing Page Analysis</div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <span class="text-sm text-gray-500">Score</span>
                  <div class="text-3xl font-bold text-primary-500">7.5/10</div>
                </div>
                <div>
                  <span class="text-sm text-gray-500">Est. Conversion</span>
                  <div class="text-3xl font-bold text-secondary-500">3.2%</div>
                </div>
              </div>
              
              <div class="mb-6">
                <h3 class="text-sm font-medium text-gray-500 mb-2">What's Good</h3>
                <ul class="space-y-2">
                  <li class="flex items-start animate-slide-up" style="animation-delay: 0.1s">
                    <svg class="h-5 w-5 text-success-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span>Clear value proposition</span>
                  </li>
                  <li class="flex items-start animate-slide-up" style="animation-delay: 0.2s">
                    <svg class="h-5 w-5 text-success-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span>Mobile responsive design</span>
                  </li>
                </ul>
              </div>
              
              <div v-if="isAnimating" class="mb-6">
                <h3 class="text-sm font-medium text-gray-500 mb-2">Generating Recommendations...</h3>
                <div class="bg-gray-100 h-6 w-full rounded-md overflow-hidden">
                  <div class="bg-primary-500 h-full animate-pulse-slow"></div>
                </div>
              </div>
              
              <div v-else class="mb-6 animate-fade-in">
                <h3 class="text-sm font-medium text-gray-500 mb-2">Recommendations</h3>
                <ul class="space-y-2">
                  <li class="flex items-start">
                    <div class="flex-shrink-0 w-5 h-5 bg-primary-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                      1
                    </div>
                    <span class="ml-2">Add testimonials to build trust</span>
                  </li>
                  <li class="flex items-start">
                    <div class="flex-shrink-0 w-5 h-5 bg-primary-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                      2
                    </div>
                    <span class="ml-2">Simplify form fields to reduce friction</span>
                  </li>
                </ul>
              </div>
              
              <button class="w-full mt-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition-colors">
                Chat with AI about this page
              </button>
            </div>
          </div>
          
          <!-- Decorative elements -->
          <div class="absolute -top-6 -right-6 w-24 h-24 bg-secondary-500 rounded-full opacity-20 animate-pulse-slow"></div>
          <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-accent-500 rounded-full opacity-20 animate-pulse-slow" style="animation-delay: 1s"></div>
        </div>
      </div>
    </div>
  </section>
</template>