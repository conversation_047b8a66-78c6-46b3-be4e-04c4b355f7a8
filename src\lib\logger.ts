
// src/lib/logger.ts

// ANSI escape codes for colors
const Colors = {
  Reset: "\x1b[0m",
  Bright: "\x1b[1m",
  Dim: "\x1b[2m",
  Underscore: "\x1b[4m",
  Blink: "\x1b[5m",
  Reverse: "\x1b[7m",
  Hidden: "\x1b[8m",

  FgBlack: "\x1b[30m",
  FgRed: "\x1b[31m",
  FgGreen: "\x1b[32m",
  FgYellow: "\x1b[33m",
  FgBlue: "\x1b[34m",
  FgMagenta: "\x1b[35m",
  FgCyan: "\x1b[36m",
  FgWhite: "\x1b[37m",
  FgGray: "\x1b[90m",

  BgBlack: "\x1b[40m",
  BgRed: "\x1b[41m",
  BgGreen: "\x1b[42m",
  BgYellow: "\x1b[43m",
  BgBlue: "\x1b[44m",
  BgMagenta: "\x1b[45m",
  Bg<PERSON>yan: "\x1b[46m",
  BgWhite: "\x1b[47m",
  BgGray: "\x1b[100m",
};

function formatLog(type: string, message: string, color: string = Colors.Reset): string {
  const timestamp = new Date().toISOString();
  return `${Colors.FgGray}[${timestamp}]${Colors.Reset} ${color}${type}:${Colors.Reset} ${message}`;
}

export const log = {
  info: (message: string) => console.log(formatLog("INFO", message, Colors.FgCyan)),
  warn: (message: string) => console.warn(formatLog("WARN", message, Colors.FgYellow)),
  error: (message: string) => console.error(formatLog("ERROR", message, Colors.FgRed)),
  debug: (message: string) => console.log(formatLog("DEBUG", message, Colors.FgMagenta)),
};

export async function loggedFetch(
  input: RequestInfo | URL,
  init?: RequestInit
): Promise<Response> {
  const url = input instanceof Request ? input.url : input.toString();
  const method = init?.method || (input instanceof Request ? input.method : "GET");
  const requestPayload = init?.body ? String(init.body) : "N/A";

  log.debug(`Outgoing Request:`);
  log.debug(`  Type: ${method}`);
  log.debug(`  Endpoint: ${url}`);
  log.debug(`  Payload: ${requestPayload}`);

  try {
    const response = await fetch(input, init);

    const responseClone = response.clone(); // Clone to allow reading body twice
    const responseStatus = responseClone.status;
    const responseStatusText = responseClone.statusText;
    let responseBody: string;

    try {
      responseBody = await responseClone.text();
      // Attempt to pretty-print JSON if applicable
      try {
        responseBody = JSON.stringify(JSON.parse(responseBody), null, 2);
      } catch (e) {
        // Not JSON, keep as is
      }
    } catch (e) {
      responseBody = `(Unable to read response body: ${e})`;
    }

    log.debug(`Incoming Response:`);
    log.debug(`  Status: ${responseStatus} ${responseStatusText}`);
    log.debug(`  Body: ${responseBody}`);

    return response;
  } catch (error: any) {
    log.error(`Request Error:`);
    log.error(`  Type: ${method}`);
    log.error(`  Endpoint: ${url}`);
    log.error(`  Error: ${error.message || error}`);
    throw error;
  }
}
