<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { Database } from '../../types/supabase';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const isVisible = ref(false);

onMounted(() => {
  // Trigger animation after component mounts
  setTimeout(() => {
    isVisible.value = true;
  }, 100);
});

const allPros = computed(() => {
  // Try to get comprehensive pros from AI analysis first
  const lighthouseData = props.analysis.lighthouse_data as any;
  if (lighthouseData?.leadInsights?.businessImpact) {
    // Use AI-generated business impact as pros
    const aiPros = lighthouseData.leadInsights.businessImpact.filter((impact: string) =>
      !impact.toLowerCase().includes('issue') && !impact.toLowerCase().includes('problem')
    );
    if (aiPros.length > 0) {
      return aiPros.slice(0, 8);
    }
  }

  // Fallback to existing logic - parse JSON strings if needed
  let conversionPros = [];
  try {
    conversionPros = Array.isArray(props.analysis.pros)
      ? props.analysis.pros
      : typeof props.analysis.pros === 'string'
        ? JSON.parse(props.analysis.pros)
        : [];
  } catch (e) {
    console.error('Error parsing pros:', e);
    conversionPros = [];
  }

  const seoPros = props.analysis.seo_data ? (props.analysis.seo_data as any).pros || [] : [];
  const performancePros = [];

  // Add performance-based pros
  if (props.analysis.performance_score && props.analysis.performance_score >= 80) {
    performancePros.push('Good page performance');
  }
  if (props.analysis.lcp_score && props.analysis.lcp_score <= 2.5) {
    performancePros.push('Fast content loading');
  }
  if (props.analysis.cls_score && props.analysis.cls_score <= 0.1) {
    performancePros.push('Stable visual layout');
  }

  // Combine and deduplicate
  const combined = [...conversionPros, ...seoPros, ...performancePros];
  return [...new Set(combined)].slice(0, 8); // Limit to 8 items
});

const allCons = computed(() => {
  // Try to get comprehensive cons from AI analysis first
  const lighthouseData = props.analysis.lighthouse_data as any;
  if (lighthouseData?.leadInsights?.businessImpact) {
    // Use AI-generated business impact as cons (negative impacts)
    const aiCons = lighthouseData.leadInsights.businessImpact.filter((impact: string) =>
      impact.toLowerCase().includes('issue') || impact.toLowerCase().includes('problem') ||
      impact.toLowerCase().includes('poor') || impact.toLowerCase().includes('slow')
    );
    if (aiCons.length > 0) {
      return aiCons.slice(0, 8);
    }
  }

  // Fallback to existing logic - parse JSON strings if needed
  let conversionCons = [];
  try {
    conversionCons = Array.isArray(props.analysis.cons)
      ? props.analysis.cons
      : typeof props.analysis.cons === 'string'
        ? JSON.parse(props.analysis.cons)
        : [];
  } catch (e) {
    console.error('Error parsing cons:', e);
    conversionCons = [];
  }

  const seoCons = props.analysis.seo_data ? (props.analysis.seo_data as any).cons || [] : [];
  const performanceCons = [];

  // Add performance-based cons
  if (props.analysis.performance_score && props.analysis.performance_score < 70) {
    performanceCons.push('Poor page performance affecting user experience');
  }
  if (props.analysis.lcp_score && props.analysis.lcp_score > 4.0) {
    performanceCons.push('Slow content loading may cause user drop-off');
  }
  if (props.analysis.fid_score && props.analysis.fid_score > 300) {
    performanceCons.push('Delayed response to user interactions');
  }
  if (props.analysis.cls_score && props.analysis.cls_score > 0.25) {
    performanceCons.push('Unstable layout causing poor user experience');
  }
  
  // Combine and deduplicate
  const combined = [...conversionCons, ...seoCons, ...performanceCons];
  return [...new Set(combined)].slice(0, 8); // Limit to 8 items
});

const overallScore = computed(() => {
  const scores = [
    props.analysis.score || 0,
    props.analysis.performance_score || 0,
    props.analysis.seo_score || 0
  ].filter(score => score > 0);
  
  if (scores.length === 0) return 0;
  return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
});

const getOverallGrade = computed(() => {
  const score = overallScore.value;
  if (score >= 90) return { grade: 'A', color: 'text-green-600', bg: 'bg-green-50' };
  if (score >= 80) return { grade: 'B', color: 'text-blue-600', bg: 'bg-blue-50' };
  if (score >= 70) return { grade: 'C', color: 'text-yellow-600', bg: 'bg-yellow-50' };
  if (score >= 60) return { grade: 'D', color: 'text-orange-600', bg: 'bg-orange-50' };
  return { grade: 'F', color: 'text-red-600', bg: 'bg-red-50' };
});

// Generate page summary based on analysis data
const pageSummary = computed(() => {
  const summary = {
    overview: '',
    keyFindings: [] as string[],
    recommendations: [] as string[],
    businessImpact: ''
  };

  // Generate overview
  const scoreText = overallScore.value >= 80 ? 'strong' : overallScore.value >= 60 ? 'moderate' : 'significant improvement needed';
  summary.overview = `This landing page analysis reveals ${scoreText} performance across conversion optimization, technical performance, and SEO factors. `;

  // Key findings
  if (props.analysis.performance_score && props.analysis.performance_score < 70) {
    summary.keyFindings.push('Page performance issues may be causing visitor drop-off');
  }
  if (props.analysis.seo_score && props.analysis.seo_score < 80) {
    summary.keyFindings.push('SEO optimization opportunities exist for better search visibility');
  }
  if (allCons.value.length > allPros.value.length) {
    summary.keyFindings.push('Multiple areas identified for conversion rate improvement');
  }
  if (allPros.value.length > 0) {
    summary.keyFindings.push(`${allPros.value.length} strong elements already working well`);
  }

  // Recommendations
  if (props.analysis.performance_score && props.analysis.performance_score < 70) {
    summary.recommendations.push('Prioritize technical performance improvements');
  }
  if (allCons.value.length > 3) {
    summary.recommendations.push('Focus on top 3 conversion optimization issues first');
  }
  summary.recommendations.push('Implement A/B testing for proposed changes');
  summary.recommendations.push('Monitor key metrics after implementing improvements');

  // Business impact
  const potentialIncrease = overallScore.value < 60 ? '25-40%' : overallScore.value < 80 ? '15-25%' : '5-15%';
  summary.businessImpact = `Implementing the recommended improvements could potentially increase conversion rates by ${potentialIncrease} based on industry benchmarks and identified optimization opportunities.`;

  return summary;
});
</script>

<template>
  <div 
    class="transition-all duration-700 ease-out transform space-y-8"
    :class="isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'"
  >
    <!-- Pros and Cons Analysis -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h3 class="text-lg font-semibold text-gray-900">Pros and Cons Analysis</h3>
        </div>
        
        <!-- Overall Grade -->
        <div class="flex items-center space-x-3">
          <div class="text-right">
            <p class="text-sm text-gray-500">Overall Grade</p>
          </div>
          <div 
            class="w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold"
            :class="[getOverallGrade.color, getOverallGrade.bg]"
          >
            {{ getOverallGrade.grade }}
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Pros -->
        <div class="space-y-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h4 class="font-medium text-green-900">Strengths</h4>
          </div>
          
          <div class="space-y-2">
            <div 
              v-for="(pro, index) in allPros" 
              :key="`pro-${index}`"
              class="flex items-start p-3 bg-green-50 rounded-lg border border-green-200 transition-all duration-300 ease-out transform"
              :class="isVisible ? 'translate-x-0 opacity-100' : '-translate-x-4 opacity-0'"
              :style="{ transitionDelay: `${index * 100 + 200}ms` }"
            >
              <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="text-sm text-green-800">{{ pro }}</span>
            </div>
            
            <div v-if="allPros.length === 0" class="text-center py-8 text-gray-500">
              <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-sm">No strengths identified yet</p>
            </div>
          </div>
        </div>

        <!-- Cons -->
        <div class="space-y-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h4 class="font-medium text-red-900">Areas for Improvement</h4>
          </div>
          
          <div class="space-y-2">
            <div 
              v-for="(con, index) in allCons" 
              :key="`con-${index}`"
              class="flex items-start p-3 bg-red-50 rounded-lg border border-red-200 transition-all duration-300 ease-out transform"
              :class="isVisible ? 'translate-x-0 opacity-100' : 'translate-x-4 opacity-0'"
              :style="{ transitionDelay: `${index * 100 + 200}ms` }"
            >
              <svg class="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
              <span class="text-sm text-red-800">{{ con }}</span>
            </div>
            
            <div v-if="allCons.length === 0" class="text-center py-8 text-gray-500">
              <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-sm">No issues identified</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Summary -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center mb-6">
        <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="text-lg font-semibold text-gray-900">Page Summary</h3>
      </div>

      <div class="space-y-6">
        <!-- Overview -->
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Overview</h4>
          <p class="text-gray-700">{{ pageSummary.overview }}</p>
        </div>

        <!-- Key Findings -->
        <div>
          <h4 class="font-medium text-gray-900 mb-3">Key Findings</h4>
          <div class="space-y-2">
            <div 
              v-for="(finding, index) in pageSummary.keyFindings" 
              :key="index"
              class="flex items-start p-3 bg-blue-50 rounded-lg border border-blue-200"
            >
              <svg class="w-4 h-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="text-sm text-blue-800">{{ finding }}</span>
            </div>
          </div>
        </div>

        <!-- Recommendations -->
        <div>
          <h4 class="font-medium text-gray-900 mb-3">Recommendations</h4>
          <div class="space-y-2">
            <div 
              v-for="(recommendation, index) in pageSummary.recommendations" 
              :key="index"
              class="flex items-start p-3 bg-yellow-50 rounded-lg border border-yellow-200"
            >
              <svg class="w-4 h-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <span class="text-sm text-yellow-800">{{ recommendation }}</span>
            </div>
          </div>
        </div>

        <!-- Business Impact -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 p-4">
          <h4 class="font-medium text-green-900 mb-2 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
            Potential Business Impact
          </h4>
          <p class="text-sm text-green-800">{{ pageSummary.businessImpact }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Additional animation styles if needed */
</style>
