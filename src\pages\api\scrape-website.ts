import type { APIRoute } from 'astro';
import { J<PERSON><PERSON> } from 'jsdom';

export const prerender = false;

export const GET: APIRoute = async ({ request }) => {
  const url = new URL(request.url).searchParams.get('url');
  
  if (!url) {
    return new Response(JSON.stringify({ error: 'URL is required' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // Validate URL
    const urlObj = new URL(url);
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      throw new Error('Invalid URL protocol');
    }

    // Fetch the website with proper headers
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; WebsiteAnalyzer/1.0)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      redirect: 'follow'
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch website: ${response.status} ${response.statusText}`);
    }
    
    const html = await response.text();
    
    // Parse HTML using JSDOM
    const dom = new JSDOM(html);
    const document = dom.window.document;
    
    // Extract title
    const title = document.querySelector('title')?.textContent?.trim() || 'No title found';
    
    // Extract meta description
    const metaDescription = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';
    const metaKeywords = document.querySelector('meta[name="keywords"]')?.getAttribute('content') || '';
    const metaAuthor = document.querySelector('meta[name="author"]')?.getAttribute('content') || '';
    
    // Extract headings
    const headings: string[] = [];
    ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].forEach(tag => {
      const elements = document.querySelectorAll(tag);
      elements.forEach(el => {
        const text = el.textContent?.trim();
        if (text) headings.push(text);
      });
    });
    
    // Extract images
    const images: string[] = [];
    const imgElements = document.querySelectorAll('img');
    imgElements.forEach(img => {
      const src = img.getAttribute('src');
      if (src) {
        // Convert relative URLs to absolute
        try {
          const absoluteUrl = new URL(src, url).toString();
          images.push(absoluteUrl);
        } catch (e) {
          // Skip invalid URLs
        }
      }
    });
    
    // Extract links
    const links: string[] = [];
    const linkElements = document.querySelectorAll('a[href]');
    linkElements.forEach(link => {
      const href = link.getAttribute('href');
      if (href) {
        try {
          const absoluteUrl = new URL(href, url).toString();
          links.push(absoluteUrl);
        } catch (e) {
          // Skip invalid URLs
        }
      }
    });
    
    // Extract main content (remove script, style, nav, footer, etc.)
    const elementsToRemove = document.querySelectorAll('script, style, nav, footer, header, aside, .nav, .footer, .header, .sidebar');
    elementsToRemove.forEach(el => el.remove());
    
    // Get text content from body
    const bodyText = document.body?.textContent || '';
    const cleanContent = bodyText
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, ' ')
      .trim();
    
    const scrapedData = {
      url,
      title,
      content: cleanContent,
      headings,
      images: images.slice(0, 50), // Limit to first 50 images
      links: links.slice(0, 100), // Limit to first 100 links
      metadata: {
        description: metaDescription,
        keywords: metaKeywords,
        author: metaAuthor
      }
    };
    
    return new Response(JSON.stringify(scrapedData), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error scraping website:', error);
    
    let errorMessage = 'Failed to scrape website';
    if (error instanceof Error) {
      if (error.message.includes('Invalid URL')) {
        errorMessage = 'Invalid URL provided';
      } else if (error.message.includes('Failed to fetch')) {
        errorMessage = 'Unable to access the website. It may be down or blocking requests.';
      } else {
        errorMessage = error.message;
      }
    }
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};