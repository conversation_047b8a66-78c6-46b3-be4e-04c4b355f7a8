import type { APIRoute } from 'astro';
import { supabase } from '../../lib/supabase';
import { analyzeLandingPage } from '../../lib/ai';
import type { AnalysisResult } from '../../types/analysis';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { url, analysisId } = await request.json();

    if (!url || !analysisId) {
      return new Response(
        JSON.stringify({ error: 'URL and analysisId are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Fetch website content
    const scrapeResponse = await fetch(new URL('/api/scrape-website', request.url).toString(), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url })
    });

    if (!scrapeResponse.ok) {
        const errorText = await scrapeResponse.text();
        console.error('Failed to scrape website:', errorText);
        return new Response(JSON.stringify({ error: 'Failed to scrape website' }), { status: 500 });
    }

    const { textContent } = await scrapeResponse.json();

    // Get AI-powered analysis
    const analysis: AnalysisResult = await analyzeLandingPage(textContent, url);

    // Save suggestions to Supabase
    const suggestions = analysis.recommendations.map((recommendation: string) => ({
        analysis_id: analysisId,
        title: recommendation,
        category: 'General',
        description: recommendation,
        impact_level: 'Medium',
        effort_level: 'Medium'
    }));

    const { error: dbError } = await supabase.from('suggestions').insert(suggestions);

    if (dbError) {
        console.error('Error saving suggestions:', dbError);
        return new Response(JSON.stringify({ error: 'Failed to save suggestions' }), { status: 500 });
    }

    // Update the analysis with the new data
    await supabase.from('analyses').update({
        pros: JSON.stringify(analysis.pros),
        cons: JSON.stringify(analysis.cons),
        target_audience: analysis.targetAudience,
        conversion_rate: analysis.conversionRate,
        score: analysis.score,
    }).eq('id', analysisId);


    return new Response(JSON.stringify({ success: true }), { status: 200 });

  } catch (error) {
    console.error('Error generating suggestions:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), { status: 500 });
  }
};