// API endpoint for running SEO analysis
import type { APIRoute } from 'astro';
// @ts-ignore
import puppeteer from 'puppeteer';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  import.meta.env.PUBLIC_SUPABASE_URL,
  import.meta.env.SUPABASE_SERVICE_ROLE_KEY
);

interface SEOIssue {
  type: string;
  severity: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  recommendation?: string;
  elementSelector?: string;
  currentValue?: string;
  suggestedValue?: string;
}

export const POST: APIRoute = async ({ request }) => {
  let browser;

  try {
    const { url, analysisId } = await request.json();

    if (!url || !analysisId) {
      return new Response(JSON.stringify({ error: 'Missing required parameters' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Normalize URL
    const normalizedUrl = url.startsWith('http') ? url : `https://${url}`;

    // Launch browser
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

    await page.goto(normalizedUrl, {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    // Extract SEO data
    const seoData = await page.evaluate(() => {
      // Title tag
      const title = document.querySelector('title')?.textContent?.trim() || '';

      // Meta description
      const metaDescription = document.querySelector('meta[name="description"]')?.getAttribute('content')?.trim() || '';

      // Canonical URL
      const canonical = document.querySelector('link[rel="canonical"]')?.getAttribute('href') || '';

      // Open Graph tags
      const ogTags: Record<string, string> = {};
      document.querySelectorAll('meta[property^="og:"]').forEach(tag => {
        const property = tag.getAttribute('property');
        const content = tag.getAttribute('content');
        if (property && content) {
          ogTags[property] = content;
        }
      });

      // Twitter Card tags
      const twitterTags: Record<string, string> = {};
      document.querySelectorAll('meta[name^="twitter:"]').forEach(tag => {
        const name = tag.getAttribute('name');
        const content = tag.getAttribute('content');
        if (name && content) {
          twitterTags[name] = content;
        }
      });

      // Heading structure
      const headings: Array<{level: number, text: string}> = [];
      document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
        headings.push({
          level: parseInt(heading.tagName.charAt(1)),
          text: heading.textContent?.trim() || ''
        });
      });

      // Images without alt text
      const imagesWithoutAlt = Array.from(document.querySelectorAll('img')).filter(img => !img.getAttribute('alt')).length;
      const totalImages = document.querySelectorAll('img').length;

      // Structured data (JSON-LD)
      const structuredData: any[] = [];
      document.querySelectorAll('script[type="application/ld+json"]').forEach(script => {
        try {
          const data = JSON.parse(script.textContent || '');
          structuredData.push(data);
        } catch (e) {
          // Invalid JSON-LD
        }
      });

      // Links analysis
      const internalLinks = Array.from(document.querySelectorAll('a[href]')).filter(link => {
        const href = link.getAttribute('href');
        return href && (href.startsWith('/') || href.includes(window.location.hostname));
      }).length;

      const externalLinks = Array.from(document.querySelectorAll('a[href]')).filter(link => {
        const href = link.getAttribute('href');
        return href && href.startsWith('http') && !href.includes(window.location.hostname);
      }).length;

      return {
        title,
        metaDescription,
        canonical,
        ogTags,
        twitterTags,
        headings,
        structuredData,
        bodyText: document.body.textContent || '',
        imagesWithoutAlt,
        totalImages,
        internalLinks,
        externalLinks,
        hasH1: headings.some(h => h.level === 1),
        h1Count: headings.filter(h => h.level === 1).length
      };
    });

    // Analyze keyword density
    const words = seoData.bodyText.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter((word: string) => word.length > 3);

    const wordCount: Record<string, number> = {};
    words.forEach((word: string) => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    // Get top keywords
    const topKeywords = Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .reduce((obj, [word, count]) => ({ ...obj, [word]: count }), {});

    // Generate SEO issues
    const issues: SEOIssue[] = [];

    // Title analysis
    if (!seoData.title) {
      issues.push({
        type: 'missing_title',
        severity: 'critical',
        title: 'Missing Title Tag',
        description: 'Page is missing a title tag',
        recommendation: 'Add a descriptive title tag (50-60 characters)'
      });
    } else {
      if (seoData.title.length < 30) {
        issues.push({
          type: 'title_too_short',
          severity: 'warning',
          title: 'Title Tag Too Short',
          description: `Title tag is only ${seoData.title.length} characters`,
          recommendation: 'Expand title to 50-60 characters for better SEO',
          currentValue: seoData.title
        });
      } else if (seoData.title.length > 60) {
        issues.push({
          type: 'title_too_long',
          severity: 'warning',
          title: 'Title Tag Too Long',
          description: `Title tag is ${seoData.title.length} characters (may be truncated)`,
          recommendation: 'Keep title under 60 characters',
          currentValue: seoData.title
        });
      }
    }

    // Meta description analysis
    if (!seoData.metaDescription) {
      issues.push({
        type: 'missing_meta_description',
        severity: 'warning',
        title: 'Missing Meta Description',
        description: 'Page is missing a meta description',
        recommendation: 'Add a compelling meta description (150-160 characters)'
      });
    } else if (seoData.metaDescription.length > 160) {
      issues.push({
        type: 'meta_description_too_long',
        severity: 'info',
        title: 'Meta Description Too Long',
        description: `Meta description is ${seoData.metaDescription.length} characters`,
        recommendation: 'Keep meta description under 160 characters',
        currentValue: seoData.metaDescription
      });
    }

    // H1 analysis
    if (!seoData.hasH1) {
      issues.push({
        type: 'missing_h1',
        severity: 'critical',
        title: 'Missing H1 Tag',
        description: 'Page is missing an H1 heading',
        recommendation: 'Add a single, descriptive H1 tag to the page'
      });
    } else if (seoData.h1Count > 1) {
      issues.push({
        type: 'multiple_h1',
        severity: 'warning',
        title: 'Multiple H1 Tags',
        description: `Page has ${seoData.h1Count} H1 tags`,
        recommendation: 'Use only one H1 tag per page'
      });
    }

    // Images analysis
    if (seoData.imagesWithoutAlt > 0) {
      issues.push({
        type: 'missing_alt_text',
        severity: 'warning',
        title: 'Images Missing Alt Text',
        description: `${seoData.imagesWithoutAlt} out of ${seoData.totalImages} images are missing alt text`,
        recommendation: 'Add descriptive alt text to all images for accessibility and SEO'
      });
    }

    // Open Graph analysis
    if (!seoData.ogTags['og:title']) {
      issues.push({
        type: 'missing_og_title',
        severity: 'info',
        title: 'Missing Open Graph Title',
        description: 'Page is missing og:title meta tag',
        recommendation: 'Add og:title for better social media sharing'
      });
    }

    if (!seoData.ogTags['og:description']) {
      issues.push({
        type: 'missing_og_description',
        severity: 'info',
        title: 'Missing Open Graph Description',
        description: 'Page is missing og:description meta tag',
        recommendation: 'Add og:description for better social media sharing'
      });
    }

    // Calculate SEO score
    let score = 100;
    issues.forEach(issue => {
      if (issue.severity === 'critical') score -= 20;
      else if (issue.severity === 'warning') score -= 10;
      else score -= 5;
    });

    // Generate pros and cons
    const pros: string[] = [];
    const cons: string[] = [];

    if (seoData.title && seoData.title.length >= 30 && seoData.title.length <= 60) {
      pros.push('Well-optimized title tag length');
    }
    if (seoData.metaDescription && seoData.metaDescription.length <= 160) {
      pros.push('Good meta description length');
    }
    if (seoData.hasH1 && seoData.h1Count === 1) {
      pros.push('Proper H1 tag usage');
    }
    if (seoData.structuredData.length > 0) {
      pros.push('Structured data implementation');
    }
    if (seoData.imagesWithoutAlt === 0 && seoData.totalImages > 0) {
      pros.push('All images have alt text');
    }
    if (seoData.ogTags['og:title'] && seoData.ogTags['og:description']) {
      pros.push('Complete Open Graph tags');
    }

    issues.forEach(issue => {
      cons.push(issue.description);
    });

    const finalSEOData = {
      ...seoData,
      keywordDensity: topKeywords,
      pros,
      cons
    };

    // Store in database
    const { error: updateError } = await supabase
      .from('analyses')
      .update({
        seo_score: Math.max(0, score),
        seo_data: finalSEOData
      })
      .eq('id', analysisId);

    if (updateError) {
      console.error('Database update error:', updateError);
    }

    await browser.close();

    return new Response(JSON.stringify({
      success: true,
      score: Math.max(0, score),
      issues,
      data: finalSEOData
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('SEO analysis error:', error);

    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error('Browser close error:', closeError);
      }
    }

    return new Response(JSON.stringify({
      error: 'SEO analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/*
Additional SEO analysis features to implement:

1. Image optimization analysis:
   - Missing alt text
   - Large image file sizes
   - Non-optimized formats

2. Technical SEO:
   - Page load speed
   - Mobile responsiveness
   - HTTPS usage
   - XML sitemap presence

3. Content analysis:
   - Keyword density
   - Content length
   - Readability score
   - Internal/external link analysis

4. Schema markup validation:
   - Structured data testing
   - Rich snippet opportunities
   - Local business markup

5. Social media optimization:
   - Open Graph tags
   - Twitter Card tags
   - Social sharing optimization

6. Accessibility SEO:
   - Semantic HTML usage
   - ARIA labels
   - Color contrast
   - Keyboard navigation

Tools and libraries for real implementation:
- Puppeteer: DOM scraping and page analysis
- Cheerio: Server-side HTML parsing
- Lighthouse: Technical SEO audits
- Schema.org validator: Structured data validation
- Readability algorithms: Content analysis
*/
