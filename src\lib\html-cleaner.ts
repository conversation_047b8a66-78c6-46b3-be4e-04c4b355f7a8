import { J<PERSON><PERSON> } from 'jsdom';

/**
 * Cleans landing page HTML to prepare it for AI evaluation.
 * Removes noise while preserving elements important for conversion analysis.
 *
 * @param rawHtml The raw HTML content of the landing page.
 * @returns A cleaned, semantic HTML string.
 */
export function cleanLandingPageHtml(rawHtml: string): string {
  const dom = new JSDOM(rawHtml);
  const { document } = dom.window;

  // 1. Remove noise tags
  const noiseTags = ['script', 'style', 'noscript', 'svg', 'iframe', 'link', 'meta', 'head'];
  noiseTags.forEach(tagName => {
    document.querySelectorAll(tagName).forEach(el => el.remove());
  });

  // 2. Remove comments
  const iterator = document.createNodeIterator(document.body, 4 /* SHOW_COMMENT */);
  let node;
  while (node = iterator.nextNode()) {
    node.parentNode?.removeChild(node);
  }

  // 3. Clean attributes and identify semantic sections
  const allElements = document.body.querySelectorAll('*');
  allElements.forEach(el => {
    // Remove non-essential attributes
    for (const attr of [...el.attributes]) {
      const attrName = attr.name.toLowerCase();
      if (!['href', 'src', 'alt', 'type', 'role', 'aria-label', 'for'].includes(attrName) && !attrName.startsWith('data-semantic')) {
        el.removeAttribute(attr.name);
      }
    }

    // Add comments for semantic sections
    const semanticRole = el.getAttribute('data-semantic');
    if (semanticRole) {
      const comment = document.createComment(` START ${semanticRole.toUpperCase()} `);
      const endComment = document.createComment(` END ${semanticRole.toUpperCase()} `);
      el.before(comment);
      el.after(endComment);
      el.removeAttribute('data-semantic');
    }
  });

  // 4. Remove empty container tags (div, section, etc.)
  const containerTags = ['div', 'section', 'article', 'header', 'footer', 'nav', 'aside'];
  let changed;
  do {
    changed = false;
    document.querySelectorAll(containerTags.join(',')).forEach(el => {
      if ((el.textContent ?? '').trim() === '' && el.children.length === 0) {
        el.remove();
        changed = true;
      }
    });
  } while (changed);

  // 5. Format the output for readability
  let cleanedHtml = document.body.innerHTML;

  // Basic formatting (indentation)
  cleanedHtml = cleanedHtml.replace(/\n\s*\n/g, '\n'); // Remove multiple blank lines

  return cleanedHtml;
}
