<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { supabase } from '../../lib/supabase';

const analyses = ref([]);
const loading = ref(true);
const searchQuery = ref('');

onMounted(async () => {
  await loadAnalyses();
});

const loadAnalyses = async () => {
  try {
    loading.value = true;
    
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) return;
    
    const { data, error } = await supabase
      .from('analyses')
      .select('id, url, title, score, created_at')
      .eq('user_id', userData.user.id)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    
    analyses.value = data || [];
  } catch (error) {
    console.error('Error loading analyses:', error);
  } finally {
    loading.value = false;
  }
};

const filteredAnalyses = computed(() => {
  if (!searchQuery.value.trim()) return analyses.value;
  
  const query = searchQuery.value.toLowerCase();
  return analyses.value.filter(analysis => 
    analysis.title.toLowerCase().includes(query) || 
    analysis.url.toLowerCase().includes(query)
  );
});

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', { 
    month: 'short', 
    day: 'numeric', 
    year: 'numeric' 
  }).format(date);
};
</script>

<template>
  <aside class="w-72 flex-shrink-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-xl border-r border-gray-200/50 dark:border-gray-700/50 overflow-y-auto">
    <div class="p-6">
      <div class="mb-8">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search analyses..."
            class="w-full pl-11 pr-4 py-3 text-sm bg-gray-100/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 backdrop-blur-sm transition-all duration-200"
          >
        </div>
      </div>
      
      <div class="mb-6">
        <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">Your Analyses</h3>
        
        <div v-if="loading" class="space-y-3">
          <div v-for="i in 5" :key="i" class="animate-pulse">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
        
        <div v-else-if="filteredAnalyses.length === 0" class="py-8 text-center">
          <div class="w-12 h-12 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p v-if="searchQuery" class="text-sm text-gray-500 dark:text-gray-400">No analyses match your search</p>
          <p v-else class="text-sm text-gray-500 dark:text-gray-400 mb-4">No analyses yet</p>
          <a 
            v-if="!searchQuery"
            href="/dashboard/analysis/new" 
            class="inline-flex items-center px-3 py-2 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
          >
            Create your first analysis
          </a>
        </div>
        
        <div v-else class="space-y-2">
          <div v-for="analysis in filteredAnalyses" :key="analysis.id">
            <a 
              :href="`/dashboard/analysis/${analysis.id}`"
              class="group block p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-all duration-200 border border-transparent hover:border-gray-200/50 dark:hover:border-gray-700/50"
            >
              <div class="flex items-center justify-between mb-2">
                <h4 class="font-medium text-gray-900 dark:text-gray-100 truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {{ analysis.title || analysis.url }}
                </h4>
                <span 
                  :class="[
                    'px-2 py-1 text-xs font-medium rounded-full',
                    analysis.score >= 8 ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400' :
                    analysis.score >= 6 ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400' :
                    'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400'
                  ]"
                >
                  {{ analysis.score }}/10
                </span>
              </div>
              <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>{{ formatDate(analysis.created_at) }}</span>
                <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                  <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
      
      <!-- Quick Actions -->
      <div class="border-t border-gray-200/50 dark:border-gray-700/50 pt-6">
        <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">Quick Actions</h3>
        <div class="space-y-2">
          <a 
            href="/dashboard/analysis/new"
            class="flex items-center p-3 rounded-xl text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 group"
          >
            <svg class="mr-3 h-4 w-4 transition-transform group-hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            New Analysis
          </a>
          <a 
            href="/dashboard/history"
            class="flex items-center p-3 rounded-xl text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-all duration-200 group"
          >
            <svg class="mr-3 h-4 w-4 transition-transform group-hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            View History
          </a>
        </div>
      </div>
    </div>
  </aside>
</template>