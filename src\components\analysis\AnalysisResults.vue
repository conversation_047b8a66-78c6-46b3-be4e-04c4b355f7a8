<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/supabase';

const props = defineProps<{
  analysisId: string;
}>();

type Analysis = Database['public']['Tables']['analyses']['Row'];

const analysis = ref<Analysis | null>(null);
const loading = ref(true);
const error = ref('');

onMounted(async () => {
  await loadAnalysis();
});

const loadAnalysis = async () => {
  try {
    loading.value = true;
    error.value = '';

    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      throw new Error('User not authenticated');
    }

    // Fetch analysis from the real database
    const { data, error: fetchError } = await supabase
      .from('analyses')
      .select('*')
      .eq('id', props.analysisId)
      .eq('user_id', userData.user.id)
      .single();

    if (fetchError) {
      throw new Error('Analysis not found or access denied');
    }

    analysis.value = data;
  } catch (e) {
    console.error('Error loading analysis:', e);
    error.value = e instanceof Error ? e.message : 'Failed to load analysis. Please try again.';
  } finally {
    loading.value = false;
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', { 
    month: 'short', 
    day: 'numeric', 
    year: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  }).format(date);
};

const getScoreColor = (score) => {
  if (score >= 8) return 'bg-success-500';
  if (score >= 6) return 'bg-warning-500';
  return 'bg-error-500';
};

const getScorePercentage = (score) => {
  return (score / 10) * 100;
};

// Helper functions to parse text fields that might be JSON or comma-separated
const parseTextArray = (text) => {
  if (!text) return [];
  // If the data is already an array, just return it.
  if (Array.isArray(text)) {
    return text;
  }

  // Ensure we're working with a string from here on.
  if (typeof text !== 'string') {
    console.warn('parseTextArray received a non-string, non-array value:', text);
    return [];
  }

  try {
    // Try to parse as JSON first. This handles strings like '["item1", "item2"]'.
    const parsed = JSON.parse(text);
    return Array.isArray(parsed) ? parsed : [text];
  } catch {
    // If not a valid JSON string, treat it as a plain string and split it.
    return text.split(/\n|,/).map(item => item.trim()).filter(item => item.length > 0);
  }
};

const prosArray = computed(() => parseTextArray(analysis.value?.pros));
const consArray = computed(() => parseTextArray(analysis.value?.cons));
const recommendationsArray = computed(() => parseTextArray(analysis.value?.recommendations));
const adaptationsArray = computed(() => parseTextArray(analysis.value?.adaptations));

const getConversionCategory = (rate) => {
  if (rate >= 5) return 'Excellent';
  if (rate >= 3) return 'Good';
  if (rate >= 2) return 'Average';
  return 'Needs improvement';
};

const getConversionColor = (rate) => {
  if (rate >= 5) return 'text-success-500';
  if (rate >= 3) return 'text-primary-500';
  if (rate >= 2) return 'text-warning-500';
  return 'text-error-500';
};
</script>

<template>
  <div>
    <div v-if="loading" class="animate-pulse space-y-4">
      <div class="h-8 bg-gray-200 rounded w-1/3"></div>
      <div class="h-4 bg-gray-200 rounded w-1/2 mt-2"></div>
      <div class="h-64 bg-gray-200 rounded w-full mt-6"></div>
    </div>
    
    <div v-else-if="error" class="bg-error-500/10 text-error-500 p-4 rounded-lg">
      {{ error }}
    </div>
    
    <div v-else-if="analysis" class="animate-fade-in">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">{{ analysis.title || 'Landing Page Analysis' }}</h1>
        <div class="mt-2 flex items-center text-sm text-gray-600">
          <a :href="analysis.url" target="_blank" rel="noopener noreferrer" class="text-primary-500 hover:underline">
            {{ analysis.url }}
            <svg class="inline-block ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </a>
          <span class="mx-2">•</span>
          <span>{{ formatDate(analysis.created_at) }}</span>
        </div>
        <div v-if="analysis.target_audience" class="mt-1 text-sm text-gray-600">
          Target Audience: {{ analysis.target_audience }}
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Overall Score</h3>
          <div class="flex items-center">
            <div class="relative flex items-center justify-center">
              <svg class="w-24 h-24" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#E5E7EB"
                  stroke-width="3"
                  stroke-dasharray="100, 100"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  :stroke="getScoreColor(analysis.score)"
                  stroke-width="3"
                  :stroke-dasharray="`${getScorePercentage(analysis.score)}, 100`"
                />
              </svg>
              <div class="absolute text-3xl font-bold">{{ analysis.score }}</div>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-600">out of 10</p>
              <p class="text-sm mt-2 font-medium">
                <span
                  :class="[
                    'px-2 py-1 rounded',
                    analysis.score >= 8 ? 'bg-success-500/10 text-success-700' :
                    analysis.score >= 6 ? 'bg-warning-500/10 text-warning-700' :
                    'bg-error-500/10 text-error-700'
                  ]"
                >
                  {{ 
                    analysis.score >= 8 ? 'Excellent' :
                    analysis.score >= 6 ? 'Good' :
                    analysis.score >= 4 ? 'Average' :
                    'Needs improvement'
                  }}
                </span>
              </p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Conversion Estimate</h3>
          <div class="flex items-center">
            <div class="text-4xl font-bold" :class="getConversionColor(analysis.conversion_rate)">
              {{ analysis.conversion_rate }}%
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-600">Predicted rate</p>
              <p class="text-sm mt-2 font-medium" :class="getConversionColor(analysis.conversion_rate)">
                {{ getConversionCategory(analysis.conversion_rate) }}
              </p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Target Audience</h3>
          <p class="text-gray-700">{{ analysis.target_audience }}</p>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow overflow-hidden mb-8">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex">
            <a href="#" class="w-1/4 py-4 px-1 text-center border-b-2 border-primary-500 font-medium text-sm text-primary-600">
              Analysis
            </a>
          </nav>
        </div>
        
        <div class="p-6">
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <svg class="h-5 w-5 text-success-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              What's Good
            </h3>
            <ul class="space-y-2">
              <li v-for="(pro, index) in prosArray" :key="index" class="flex items-start">
                <svg class="h-5 w-5 text-success-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span>{{ pro }}</span>
              </li>
            </ul>
          </div>
          
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <svg class="h-5 w-5 text-error-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
              What's Bad
            </h3>
            <ul class="space-y-2">
              <li v-for="(con, index) in consArray" :key="index" class="flex items-start">
                <svg class="h-5 w-5 text-error-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                <span>{{ con }}</span>
              </li>
            </ul>
          </div>
          
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <svg class="h-5 w-5 text-primary-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
              </svg>
              Recommendations
            </h3>
            <ul class="space-y-4">
              <li v-for="(rec, index) in recommendationsArray" :key="index" class="bg-primary-500/5 p-4 rounded-lg">
                <div class="flex items-start">
                  <div class="flex-shrink-0 w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {{ index + 1 }}
                  </div>
                  <span class="ml-3">{{ rec }}</span>
                </div>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <svg class="h-5 w-5 text-accent-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
              </svg>
              Audience Adaptations
            </h3>
            <ul class="space-y-2">
              <li v-for="(adapt, index) in adaptationsArray" :key="index" class="flex items-start p-2 hover:bg-gray-50 rounded-md transition-colors">
                <svg class="h-5 w-5 text-accent-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                </svg>
                <span>{{ adapt }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>