---
const steps = [
  {
    number: "01",
    title: "Sign in with Google",
    description: "Create an account or sign in securely with your Google account in just a few seconds.",
  },
  {
    number: "02",
    title: "Submit your landing page URL",
    description: "Enter the URL of any landing page you want to analyze. Our AI will fetch and process the content.",
  },
  {
    number: "03",
    title: "Get detailed analysis",
    description: "Receive a comprehensive analysis with scoring, conversion rate estimate, and actionable recommendations.",
  },
  {
    number: "04",
    title: "Chat with AI for deeper insights",
    description: "Start a conversation with our AI to ask specific questions and get customized advice about your page.",
  }
];
---

<section id="how-it-works" class="py-16 md:py-24 bg-gradient-to-b from-white to-gray-50">
  <div class="container mx-auto px-6">
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900">How It Works</h2>
      <p class="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
        Get actionable insights about your landing page in minutes with our simple four-step process.
      </p>
    </div>
    
    <div class="max-w-5xl mx-auto">
      <div class="relative">
        <!-- Vertical timeline line -->
        <div class="hidden md:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gray-200 transform -translate-x-1/2"></div>
        
        {steps.map((step, index) => (
          <div class={`relative z-10 md:flex items-center ${index !== steps.length - 1 ? 'mb-16' : ''}`}>
            <div class={`md:w-1/2 ${index % 2 === 0 ? 'md:pr-12 text-right' : 'md:order-1 md:pl-12'}`}>
              <div class="mb-2">
                <span class="text-sm font-bold text-primary-500">{step.number}</span>
              </div>
              <h3 class="text-xl font-bold text-gray-900">{step.title}</h3>
              <p class="mt-2 text-gray-600">{step.description}</p>
            </div>
            
            <div class="hidden md:flex items-center justify-center absolute left-1/2 transform -translate-x-1/2 -mt-2">
              <div class="w-8 h-8 rounded-full border-4 border-primary-500 bg-white"></div>
            </div>
            
            <!-- Mobile step indicator -->
            <div class="flex md:hidden items-center mt-4 mb-6">
              <div class="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-primary-500 text-white font-bold text-lg">
                {step.number.substring(1)}
              </div>
              <div class="ml-4">
                <h3 class="text-xl font-bold text-gray-900">{step.title}</h3>
                <p class="mt-1 text-gray-600">{step.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>