{"name": "convertiq", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev --host", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/check": "^0.5.9", "@astrojs/tailwind": "^5.1.0", "@astrojs/vue": "^4.0.9", "@headlessui/vue": "^1.7.23", "@supabase/supabase-js": "^2.39.7", "@types/jsdom": "^21.1.7", "@types/node": "^20.11.28", "astro": "^4.5.6", "chrome-launcher": "^1.1.2", "framer-motion": "^12.23.6", "jsdom": "^26.1.0", "lighthouse": "^11.7.1", "lucide-react": "^0.525.0", "lucide-vue-next": "^0.525.0", "openai": "^4.28.4", "puppeteer": "^22.6.5", "tailwindcss": "^3.4.1", "typescript": "^5.4.2", "vue": "^3.4.21"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "autoprefixer": "^10.4.17", "postcss": "^8.4.35"}}