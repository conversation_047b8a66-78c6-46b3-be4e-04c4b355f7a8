<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface Props {
  delay?: number;
  className?: string;
}

const props = withDefaults(defineProps<Props>(), {
  delay: 0,
  className: ''
});

const cardRef = ref<HTMLElement>();
const isVisible = ref(false);

onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, props.delay);
});
</script>

<template>
  <div
    ref="cardRef"
    :class="[
      'transition-all duration-700 ease-out',
      isVisible 
        ? 'opacity-100 translate-y-0' 
        : 'opacity-0 translate-y-4',
      className
    ]"
  >
    <slot />
  </div>
</template>