<script setup lang="ts">
import { ref } from 'vue';
import { supabase } from '../../lib/supabase';
import { fetchLandingPageContent } from '../../lib/url-parser';
import { analyzeLandingPage } from '../../lib/ai';
import { validateURL, normalizeURL } from './URLValidator';

const url = ref('');
const loading = ref(false);
const error = ref('');

const analyzeUrl = async () => {
  const normalizedUrl = normalizeURL(url.value);
  const validation = validateURL(normalizedUrl);
  
  if (!validation.isValid) {
    error.value = validation.error || 'Invalid URL';
    return;
  }
  
  try {
    loading.value = true;
    error.value = '';
    
    // Get current user
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) {
      throw new Error('You must be logged in to analyze a URL');
    }
    
    // Fetch landing page content
    const html = await fetchLandingPageContent(normalizedUrl);
    
    // Analyze the content
    const analysis = await analyzeLandingPage(html, normalizedUrl);
    
    // Save analysis to database
    const title = new URL(normalizedUrl).hostname;
    
    const { data, error: insertError } = await supabase
      .from('analyses')
      .insert({
        user_id: userData.user.id,
        url: normalizedUrl,
        title: title,
        score: analysis.score,
        conversion_rate: analysis.conversionRate,
        pros: analysis.pros,
        cons: analysis.cons,
        recommendations: analysis.recommendations,
        target_audience: analysis.targetAudience,
        adaptations: analysis.adaptations
      })
      .select('id')
      .single();
    
    if (insertError) throw insertError;
    
    // Redirect to analysis page
    window.location.href = `/dashboard/analysis/${data.id}`;
    
  } catch (e) {
    console.error('Error analyzing URL:', e);
    error.value = e.message || 'An error occurred. Please try again.';
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="p-6">
      <div class="flex flex-col space-y-4">
        <div>
          <label for="url" class="block text-sm font-medium text-gray-700 mb-1">
            Enter landing page URL
          </label>
          <input
            id="url"
            v-model="url"
            type="text"
            placeholder="https://example.com or example.com"
            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            :disabled="loading"
            @keyup.enter="analyzeUrl"
          >
          <p v-if="error" class="mt-2 text-sm text-error-500 flex items-start">
            <svg class="h-4 w-4 text-error-500 mr-1 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            {{ error }}
          </p>
        </div>
        
        <button
          @click="analyzeUrl"
          :disabled="loading"
          class="w-full px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="loading" class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Analyzing...
          </span>
          <span v-else>Analyze Landing Page</span>
        </button>
      </div>
      
      <div class="mt-6">
        <h4 class="text-sm font-medium text-gray-700 mb-2">How it works:</h4>
        <ol class="text-sm text-gray-600 space-y-2 list-decimal list-inside">
          <li>Paste your landing page URL</li>
          <li>Our AI analyzes the page content</li>
          <li>Get detailed score and recommendations</li>
          <li>Chat with AI about your results</li>
        </ol>
      </div>
    </div>
  </div>
</template>