---
import Layout from './Layout.astro';
import Navbar from '../components/dashboard/Navbar.vue';

interface Props {
  title: string;
  description?: string;
}

const { title, description } = Astro.props;
---

<Layout title={title} description={description} isAuthenticated={true}>
  <div class="flex-1 flex flex-col overflow-hidden">
    <Navbar client:load />
    <main class="flex-1 overflow-y-auto bg-background-secondary flex flex-col">
      <slot />
    </main>
  </div>
</Layout>