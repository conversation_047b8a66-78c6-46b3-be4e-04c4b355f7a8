<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';

const loading = ref(false);
const error = ref('');
const redirecting = ref(false);

// Check if user is already logged in
onMounted(async () => {
  const { data } = await supabase.auth.getSession();
  if (data.session) {
    redirecting.value = true;
    window.location.href = '/dashboard';
  }
});

const signInWithGoogle = async () => {
  try {
    loading.value = true;
    error.value = '';
    
    const { data, error: signInError } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/dashboard`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    });
    
    if (signInError) {
      throw signInError;
    }

    // If we have a URL, redirect to it
    if (data?.url) {
      window.location.href = data.url;
    }
  } catch (e) {
    console.error('Error signing in with Google:', e);
    error.value = e.message || 'An error occurred during sign in';
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <div>
    <div v-if="redirecting" class="text-center mb-4">
      <p class="text-gray-600">You're already signed in. Redirecting to dashboard...</p>
      <div class="mt-4 flex justify-center">
        <div class="animate-pulse-slow h-6 w-6 rounded-full bg-primary-500"></div>
      </div>
    </div>
    
    <div v-else>
      <div v-if="error" class="mb-4 p-3 bg-error-500/10 text-error-500 rounded">
        {{ error }}
      </div>
      
      <button 
        @click="signInWithGoogle" 
        type="button"
        :disabled="loading"
        class="w-full flex items-center justify-center gap-2 py-3 px-4 bg-white border border-gray-300 rounded-md shadow-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
      >
        <svg class="h-5 w-5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
          <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
          <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
          <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
        </svg>
        <span>{{ loading ? 'Signing in...' : 'Sign in with Google' }}</span>
      </button>
      
      <div class="mt-6 text-center">
        <p class="text-sm text-gray-600">
          By signing in, you agree to our
          <a href="#" class="text-primary-500 hover:text-primary-600">Terms of Service</a>
          and
          <a href="#" class="text-primary-500 hover:text-primary-600">Privacy Policy</a>
        </p>
      </div>
    </div>
  </div>
</template>