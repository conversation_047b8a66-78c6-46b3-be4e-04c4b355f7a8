export interface AnalysisResult {
  score: number;
  conversionRate: number;
  pros: string[];
  cons: string[];
  recommendations: string[];
  targetAudience: string;
  adaptations: string[];
}

export interface AnalysisWithMetadata extends AnalysisResult {
  id: string;
  user_id: string;
  url: string;
  title: string;
  created_at: string;
  updated_at: string;
  analysis_data?: {
    seoScore: number;
    contentQuality: number;
    userExperience: number;
    technicalScore: number;
    summary: string;
    scrapedData: any;
  };
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  created_at: string;
}