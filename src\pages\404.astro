---
import Layout from '../layouts/Layout.astro';
import { House } from 'lucide-vue-next';
---

<Layout title="Page Not Found - ConvertIQ">
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4">
    <div class="text-center">
      <div class="mb-8">
        <img src="/logo.svg" alt="ConvertIQ Logo" class="h-16 w-auto mx-auto mb-4">
        <span class="text-2xl font-bold text-primary-500">ConvertIQ</span>
      </div>
      
      <h1 class="text-9xl font-bold text-gray-200 mb-4">404</h1>
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Page Not Found</h2>
      <p class="text-xl text-gray-600 mb-8 max-w-md mx-auto">
        Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
      </p>
      
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a 
          href="/" 
          class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-500 hover:bg-primary-600 transition-colors"
        >
          <House class='mr-2 h-5 w-5 text-current'/>
          Go Home
        </a>
        <a 
          href="/dashboard" 
          class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
        >
          <svg class="mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          Dashboard
        </a>
      </div>
      
      <div class="mt-12">
        <p class="text-sm text-gray-500 mb-4">Need help? Here are some popular pages:</p>
        <div class="flex flex-wrap justify-center gap-4 text-sm">
          <a href="/about" class="text-primary-500 hover:text-primary-600">About</a>
          <a href="/pricing" class="text-primary-500 hover:text-primary-600">Pricing</a>
          <a href="/contact" class="text-primary-500 hover:text-primary-600">Contact</a>
          <a href="/login" class="text-primary-500 hover:text-primary-600">Sign In</a>
        </div>
      </div>
    </div>
  </div>
</Layout>