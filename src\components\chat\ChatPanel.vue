<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { supabase } from '../../lib/supabase';
import { chatWithAI } from '../../lib/ai';
import type { AnalysisWithMetadata, Message } from '../../types/analysis';

const props = defineProps<{
  analysisId: string;
}>();

const analysis = ref<AnalysisWithMetadata | null>(null);
const messages = ref<Message[]>([]);
const newMessage = ref('');
const loading = ref(true);
const sending = ref(false);
const error = ref('');
const messageEnd = ref<HTMLElement | null>(null);

onMounted(async () => {
  await Promise.all([
    loadAnalysis(),
    loadMessages()
  ]);
  
  scrollToBottom();
});

// Watch for new messages and scroll to bottom
watch(() => messages.value.length, () => {
  scrollToBottom();
});

const loadAnalysis = async () => {
  try {
    const { data, error: fetchError } = await supabase
      .from('analyses')
      .select('*')
      .eq('id', props.analysisId)
      .single();
    
    if (fetchError) throw fetchError;
    
    analysis.value = data;
  } catch (e) {
    console.error('Error loading analysis:', e);
    error.value = 'Failed to load analysis data';
  } finally {
    loading.value = false;
  }
};

const loadMessages = async () => {
  try {
    const { data, error: fetchError } = await supabase
      .from('messages')
      .select('*')
      .eq('analysis_id', props.analysisId)
      .order('created_at', { ascending: true });
    
    if (fetchError) throw fetchError;
    
    messages.value = data || [];
    
    // If no messages, add a welcome message
    if (data.length === 0 && analysis.value) {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData?.user) return;
      
      const welcomeMessage = `Hi there! I've analyzed your landing page at ${analysis.value.url}. The page scored ${analysis.value.score}/10 with an estimated conversion rate of ${analysis.value.conversion_rate}%. What would you like to know more about?`;
      
      const { data: msgData, error: msgError } = await supabase
        .from('messages')
        .insert({
          analysis_id: props.analysisId,
          user_id: userData.user.id,
          content: welcomeMessage,
          role: 'assistant'
        })
        .select()
        .single();
      
      if (msgError) throw msgError;
      
      messages.value = [msgData];
    }
  } catch (e) {
    console.error('Error loading messages:', e);
    error.value = 'Failed to load chat messages';
  }
};

const scrollToBottom = () => {
  setTimeout(() => {
    messageEnd.value?.scrollIntoView({ behavior: 'smooth' });
  }, 100);
};

const sendMessage = async () => {
  if (!newMessage.value.trim() || sending.value || !analysis.value) return;
  
  try {
    sending.value = true;
    
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) return;
    
    // Save user message
    const { data: userMsgData, error: userMsgError } = await supabase
      .from('messages')
      .insert({
        analysis_id: props.analysisId,
        user_id: userData.user.id,
        content: newMessage.value,
        role: 'user'
      })
      .select()
      .single();
    
    if (userMsgError) throw userMsgError;
    
    messages.value.push(userMsgData);
    
    // Clear input
    const userQuery = newMessage.value;
    newMessage.value = '';
    
    // Format messages for AI
    const chatMessages = messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
    
    // Get AI response
    const aiResponse = await chatWithAI(
      chatMessages,
      {
        score: analysis.value.score,
        conversionRate: analysis.value.conversion_rate,
        pros: analysis.value.pros,
        cons: analysis.value.cons,
        recommendations: analysis.value.recommendations,
        targetAudience: analysis.value.target_audience,
        adaptations: analysis.value.adaptations
      },
      analysis.value.url
    );
    
    // Save AI response
    const { data: aiMsgData, error: aiMsgError } = await supabase
      .from('messages')
      .insert({
        analysis_id: props.analysisId,
        user_id: userData.user.id,
        content: aiResponse,
        role: 'assistant'
      })
      .select()
      .single();
    
    if (aiMsgError) throw aiMsgError;
    
    messages.value.push(aiMsgData);
    
  } catch (e) {
    console.error('Error sending message:', e);
    error.value = 'Failed to send message. Please try again.';
  } finally {
    sending.value = false;
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', { 
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  }).format(date);
};
</script>

<template>
  <div class="bg-white rounded-lg shadow overflow-hidden flex flex-col h-[500px]">
    <div v-if="loading" class="flex-1 p-4 flex items-center justify-center">
      <div class="animate-pulse flex space-x-4">
        <div class="flex-1 space-y-4 py-1">
          <div class="h-4 bg-gray-200 rounded w-3/4"></div>
          <div class="space-y-2">
            <div class="h-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else-if="error" class="flex-1 p-4 flex items-center justify-center">
      <div class="text-error-500">{{ error }}</div>
    </div>
    
    <div v-else class="flex-1 p-4 overflow-y-auto" id="chat-messages">
      <div v-for="message in messages" :key="message.id" class="mb-4">
        <div 
          :class="[
            'max-w-[80%] rounded-lg p-3', 
            message.role === 'user' 
              ? 'bg-primary-500 text-white ml-auto' 
              : 'bg-gray-100 text-gray-800'
          ]"
        >
          <div class="whitespace-pre-wrap">{{ message.content }}</div>
          <div 
            :class="[
              'text-xs mt-1 text-right', 
              message.role === 'user' ? 'text-primary-100' : 'text-gray-500'
            ]"
          >
            {{ formatDate(message.created_at) }}
          </div>
        </div>
      </div>
      
      <div v-if="sending" class="max-w-[80%] bg-gray-100 text-gray-800 rounded-lg p-3 mb-4 animate-pulse">
        <div class="flex items-center space-x-2">
          <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"></div>
          <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
          <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
        </div>
      </div>
      
      <div ref="messageEnd"></div>
    </div>
    
    <div class="border-t border-gray-200 p-4">
      <form @submit.prevent="sendMessage" class="flex items-center">
        <input
          v-model="newMessage"
          type="text"
          placeholder="Ask a question about your landing page..."
          class="flex-1 border border-gray-300 rounded-l-md p-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          :disabled="sending"
        >
        <button
          type="submit"
          :disabled="!newMessage.trim() || sending"
          class="bg-primary-500 text-white px-4 py-2 rounded-r-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 transition-colors"
        >
          <svg v-if="sending" class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </form>
    </div>
  </div>
</template>