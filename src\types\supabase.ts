export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          display_name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          display_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          display_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      analyses: {
        Row: {
          id: string
          user_id: string
          url: string
          title: string
          score: number
          conversion_rate: number
          pros: Json
          cons: Json
          recommendations: Json
          target_audience: string
          adaptations: Json
          screenshot_url: string | null
          screenshot_date: string | null
          priority_issues_count: number
          suggestions_count: number
          performance_score: number | null
          performance_grade: 'A' | 'B' | 'C' | 'D' | 'F' | null
          lcp_score: number | null
          fid_score: number | null
          cls_score: number | null
          seo_score: number | null
          lighthouse_data: Json | null
          seo_data: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          url: string
          title: string
          score: number
          conversion_rate: number
          pros: Json
          cons: Json
          recommendations: Json
          target_audience: string
          adaptations: Json
          screenshot_url?: string | null
          screenshot_date?: string | null
          priority_issues_count?: number
          suggestions_count?: number
          performance_score?: number | null
          performance_grade?: 'A' | 'B' | 'C' | 'D' | 'F' | null
          lcp_score?: number | null
          fid_score?: number | null
          cls_score?: number | null
          seo_score?: number | null
          lighthouse_data?: Json | null
          seo_data?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          url?: string
          title?: string
          score?: number
          conversion_rate?: number
          pros?: Json
          cons?: Json
          recommendations?: Json
          target_audience?: string
          adaptations?: Json
          screenshot_url?: string | null
          screenshot_date?: string | null
          priority_issues_count?: number
          suggestions_count?: number
          performance_score?: number | null
          performance_grade?: 'A' | 'B' | 'C' | 'D' | 'F' | null
          lcp_score?: number | null
          fid_score?: number | null
          cls_score?: number | null
          seo_score?: number | null
          lighthouse_data?: Json | null
          seo_data?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          analysis_id: string
          user_id: string
          content: string
          role: string
          created_at: string
        }
        Insert: {
          id?: string
          analysis_id: string
          user_id: string
          content: string
          role: string
          created_at?: string
        }
        Update: {
          id?: string
          analysis_id?: string
          user_id?: string
          content?: string
          role?: string
          created_at?: string
        }
      }
      suggestions: {
        Row: {
          id: string
          analysis_id: string
          category: string
          title: string
          description: string
          detailed_explanation: string | null
          impact_level: 'High' | 'Medium' | 'Low'
          effort_level: 'High' | 'Medium' | 'Low'
          priority: number
          created_at: string
        }
        Insert: {
          id?: string
          analysis_id: string
          category: string
          title: string
          description: string
          detailed_explanation?: string | null
          impact_level: 'High' | 'Medium' | 'Low'
          effort_level: 'High' | 'Medium' | 'Low'
          priority?: number
          created_at?: string
        }
        Update: {
          id?: string
          analysis_id?: string
          category?: string
          title?: string
          description?: string
          detailed_explanation?: string | null
          impact_level?: 'High' | 'Medium' | 'Low'
          effort_level?: 'High' | 'Medium' | 'Low'
          priority?: number
          created_at?: string
        }
      }
      suggestion_categories: {
        Row: {
          id: string
          name: string
          icon: string | null
          color: string | null
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          icon?: string | null
          color?: string | null
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          icon?: string | null
          color?: string | null
          description?: string | null
          created_at?: string
        }
      }
      performance_metrics: {
        Row: {
          id: string
          analysis_id: string
          metric_name: string
          metric_value: number
          metric_unit: string | null
          metric_category: string | null
          is_good: boolean | null
          threshold_good: number | null
          threshold_poor: number | null
          created_at: string
        }
        Insert: {
          id?: string
          analysis_id: string
          metric_name: string
          metric_value: number
          metric_unit?: string | null
          metric_category?: string | null
          is_good?: boolean | null
          threshold_good?: number | null
          threshold_poor?: number | null
          created_at?: string
        }
        Update: {
          id?: string
          analysis_id?: string
          metric_name?: string
          metric_value?: number
          metric_unit?: string | null
          metric_category?: string | null
          is_good?: boolean | null
          threshold_good?: number | null
          threshold_poor?: number | null
          created_at?: string
        }
      }
      seo_issues: {
        Row: {
          id: string
          analysis_id: string
          issue_type: string
          severity: 'critical' | 'warning' | 'info'
          title: string
          description: string
          recommendation: string | null
          element_selector: string | null
          current_value: string | null
          suggested_value: string | null
          created_at: string
        }
        Insert: {
          id?: string
          analysis_id: string
          issue_type: string
          severity: 'critical' | 'warning' | 'info'
          title: string
          description: string
          recommendation?: string | null
          element_selector?: string | null
          current_value?: string | null
          suggested_value?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          analysis_id?: string
          issue_type?: string
          severity?: 'critical' | 'warning' | 'info'
          title?: string
          description?: string
          recommendation?: string | null
          element_selector?: string | null
          current_value?: string | null
          suggested_value?: string | null
          created_at?: string
        }
      }
    }
  }
}