import type { APIRoute } from 'astro';
import { supabase } from '../../lib/supabase';
import { generateComprehensiveAnalysis } from '../../lib/ai';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const { url, analysisId } = await request.json();

    if (!url || !analysisId) {
      return new Response(
        JSON.stringify({ error: 'URL and analysisId are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Fetch website content using GET method
    const scrapeResponse = await fetch(new URL(`/api/scrape-website?url=${encodeURIComponent(url)}`, request.url).toString());

    if (!scrapeResponse.ok) {
        const errorText = await scrapeResponse.text();
        console.error('Failed to scrape website:', errorText);
        return new Response(JSON.stringify({ error: 'Failed to scrape website' }), { status: 500 });
    }

    const scrapedData = await scrapeResponse.json();
    const textContent = scrapedData.content || scrapedData.textContent;

    // Get comprehensive AI-powered analysis
    const analysis = await generateComprehensiveAnalysis(textContent, url);

    // Save comprehensive suggestions to Supabase
    const suggestions = analysis.recommendations.map((recommendation: string, index: number) => ({
        analysis_id: analysisId,
        title: recommendation,
        category: 'General',
        description: recommendation,
        impact_level: index < 3 ? 'High' : index < 6 ? 'Medium' : 'Low',
        effort_level: 'Medium',
        priority: index + 1
    }));

    // Add performance recommendations
    if (analysis.performanceInsights?.recommendations) {
      analysis.performanceInsights.recommendations.forEach((rec, index) => {
        suggestions.push({
          analysis_id: analysisId,
          title: rec.title,
          category: rec.category,
          description: rec.description,
          impact_level: rec.impact,
          effort_level: rec.effort,
          priority: suggestions.length + index + 1
        });
      });
    }

    const { error: dbError } = await supabase.from('suggestions').insert(suggestions);

    if (dbError) {
        console.error('Error saving suggestions:', dbError);
        return new Response(JSON.stringify({ error: 'Failed to save suggestions' }), { status: 500 });
    }

    // Save SEO issues if they exist
    if (analysis.seoInsights?.issues) {
      const seoIssues = analysis.seoInsights.issues.map((issue, index) => ({
        analysis_id: analysisId,
        type: issue.type,
        severity: issue.severity,
        title: issue.title,
        description: issue.description,
        recommendation: issue.recommendation,
        priority: index + 1
      }));

      const { error: seoError } = await supabase.from('seo_issues').insert(seoIssues);
      if (seoError) {
        console.error('Error saving SEO issues:', seoError);
      }
    }

    // Update the analysis with comprehensive data
    const updateData = {
        pros: JSON.stringify(analysis.pros),
        cons: JSON.stringify(analysis.cons),
        target_audience: analysis.targetAudience,
        conversion_rate: analysis.conversionRate,
        score: analysis.score,
        performance_score: analysis.performanceInsights?.score || null,
        performance_grade: analysis.performanceInsights?.grade || null,
        seo_score: analysis.seoInsights?.score || null,
        seo_data: JSON.stringify({
          pros: analysis.seoInsights?.pros || [],
          cons: analysis.seoInsights?.cons || [],
          issues: analysis.seoInsights?.issues || []
        }),
        lighthouse_data: JSON.stringify({
          performanceInsights: analysis.performanceInsights,
          leadInsights: analysis.leadInsights,
          overallGrade: analysis.overallGrade
        }),
        suggestions_count: suggestions.length,
        priority_issues_count: analysis.seoInsights?.issues?.filter(issue => issue.severity === 'critical').length || 0
    };

    await supabase.from('analyses').update(updateData).eq('id', analysisId);

    return new Response(JSON.stringify({ 
      success: true,
      analysis: analysis,
      suggestionsCount: suggestions.length
    }), { status: 200 });

  } catch (error) {
    console.error('Error generating comprehensive analysis:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), { status: 500 });
  }
};
