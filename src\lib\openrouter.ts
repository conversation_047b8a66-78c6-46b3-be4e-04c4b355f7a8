const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

if (!OPENROUTER_API_KEY) {
  throw new Error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
}

export interface WebsiteAnalysis {
  seoScore: number;
  contentQuality: number;
  userExperience: number;
  technicalScore: number;
  overallScore: number;
  seoSuggestions: string[];
  contentSuggestions: string[];
  uxSuggestions: string[];
  technicalSuggestions: string[];
  summary: string;
  strengths: string[];
  weaknesses: string[];
}

export async function analyzeWebsiteContent(
  url: string,
  content: string,
  title: string,
  headings: string[],
  images: string[]
): Promise<WebsiteAnalysis> {
  try {
    const prompt = `Analyze this website comprehensively and provide detailed insights:

URL: ${url}
Title: ${title}
Headings: ${headings.join(', ')}
Number of images: ${images.length}
Content preview: ${content.substring(0, 2000)}...

Please analyze the website across these dimensions:
1. SEO Optimization (score 1-10)
2. Content Quality (score 1-10)
3. User Experience (score 1-10)
4. Technical Implementation (score 1-10)

Provide specific, actionable suggestions for each category.
Also provide an overall assessment with strengths and weaknesses.

Respond in JSON format with this structure:
{
  "seoScore": number,
  "contentQuality": number,
  "userExperience": number,
  "technicalScore": number,
  "overallScore": number,
  "seoSuggestions": ["suggestion1", "suggestion2"],
  "contentSuggestions": ["suggestion1", "suggestion2"],
  "uxSuggestions": ["suggestion1", "suggestion2"],
  "technicalSuggestions": ["suggestion1", "suggestion2"],
  "summary": "Overall assessment summary",
  "strengths": ["strength1", "strength2"],
  "weaknesses": ["weakness1", "weakness2"]
}`;

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Website Analysis Platform'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-sonnet',
        messages: [
          {
            role: 'system',
            content: 'You are an expert website analyst specializing in SEO, UX, and technical optimization. Provide detailed, actionable insights.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        response_format: { type: 'json_object' }
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.statusText}`);
    }

    const data = await response.json();
    const analysisText = data.choices[0].message.content;
    
    return JSON.parse(analysisText);
  } catch (error) {
    console.error('Error analyzing website:', error);
    throw new Error('Failed to analyze website content');
  }
}

export async function chatWithAI(
  messages: { role: string; content: string }[],
  websiteContext: {
    url: string;
    analysis: WebsiteAnalysis;
    content: string;
  }
): Promise<string> {
  try {
    const systemPrompt = `You are an expert website analyst helping users understand and improve their website. 

Context about the website being discussed:
URL: ${websiteContext.url}
Overall Score: ${websiteContext.analysis.overallScore}/10
SEO Score: ${websiteContext.analysis.seoScore}/10
Content Quality: ${websiteContext.analysis.contentQuality}/10
UX Score: ${websiteContext.analysis.userExperience}/10
Technical Score: ${websiteContext.analysis.technicalScore}/10

Key Strengths: ${websiteContext.analysis.strengths.join(', ')}
Key Weaknesses: ${websiteContext.analysis.weaknesses.join(', ')}

Website content preview: ${websiteContext.content.substring(0, 1000)}...

Provide helpful, specific advice based on this analysis. Be conversational but professional.`;

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Website Analysis Platform'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-sonnet',
        messages: [
          { role: 'system', content: systemPrompt },
          ...messages
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error chatting with AI:', error);
    throw new Error('Failed to get AI response');
  }
}
