import { ref, computed } from 'vue';

export type Theme = 'light' | 'dark';

const theme = ref<Theme>('light');

// Check for saved theme preference or default to 'light'
if (typeof window !== 'undefined') {
  const savedTheme = localStorage.getItem('convertiq-theme') as Theme;
  if (savedTheme) {
    theme.value = savedTheme;
  } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
    theme.value = 'dark';
  }
}

export const useTheme = () => {
  const isDark = computed(() => theme.value === 'dark');
  
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light';
    if (typeof window !== 'undefined') {
      localStorage.setItem('convertiq-theme', theme.value);
      document.documentElement.classList.toggle('dark', theme.value === 'dark');
    }
  };

  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme;
    if (typeof window !== 'undefined') {
      localStorage.setItem('convertiq-theme', newTheme);
      document.documentElement.classList.toggle('dark', newTheme === 'dark');
    }
  };

  // Apply theme on mount
  if (typeof window !== 'undefined') {
    document.documentElement.classList.toggle('dark', theme.value === 'dark');
  }

  return {
    theme: computed(() => theme.value),
    isDark,
    toggleTheme,
    setTheme
  };
};