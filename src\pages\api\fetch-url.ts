import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ request }) => {
  try {
    const requestUrl = new URL(request.url);
    const targetUrl = requestUrl.searchParams.get('url');

    console.log('GET request - received URL:', targetUrl);

    if (!targetUrl || targetUrl.trim() === '') {
      return new Response(JSON.stringify({ 
        error: 'URL parameter is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Normalize URL
    let normalizedUrl = targetUrl.trim();
    if (!normalizedUrl.match(/^https?:\/\//)) {
      normalizedUrl = 'https://' + normalizedUrl;
    }

    // Validate URL
    let urlObj;
    try {
      urlObj = new URL(normalizedUrl);
    } catch (e) {
      return new Response(JSON.stringify({
        error: 'Invalid URL format. Please provide a valid URL (e.g., https://example.com)'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return new Response(JSON.stringify({
        error: 'Invalid URL protocol. Only HTTP and HTTPS are supported.'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Block localhost and private IPs for security
    if (urlObj.hostname === 'localhost' ||
        urlObj.hostname.startsWith('127.') ||
        urlObj.hostname.startsWith('192.168.') ||
        urlObj.hostname.startsWith('10.') ||
        urlObj.hostname.includes('internal')) {
      return new Response(JSON.stringify({
        error: 'Cannot fetch local or private URLs for security reasons.'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log('Fetching URL:', normalizedUrl);

    // Fetch the target URL
    const response = await fetch(normalizedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ConvertIQ/1.0)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive'
      },
      redirect: 'follow',
      signal: AbortSignal.timeout(15000)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    console.log('Successfully fetched HTML, length:', html.length);

    return new Response(JSON.stringify({ 
      html,
      url: normalizedUrl,
      status: response.status
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in fetch-url API:', error);

    let errorMessage = 'Failed to fetch URL. Please check the URL and try again.';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        errorMessage = 'Request timeout. The website took too long to respond.';
        statusCode = 408;
      } else if (error.message.includes('fetch') || error.message.includes('ENOTFOUND')) {
        errorMessage = 'Unable to access the website. It may be down or blocking requests.';
        statusCode = 502;
      } else if (error.message.includes('ECONNREFUSED')) {
        errorMessage = 'Connection refused. The website may be temporarily unavailable.';
        statusCode = 503;
      } else if (error.message.includes('certificate') || error.message.includes('SSL')) {
        errorMessage = 'SSL certificate error. The website may have security issues.';
        statusCode = 502;
      }
    }

    return new Response(JSON.stringify({ 
      error: errorMessage,
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: statusCode,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const POST: APIRoute = async ({ request }) => {
  try {
    let targetUrl: string;

    // Check content type and parse accordingly
    const contentType = request.headers.get('content-type') || '';

    if (contentType.includes('application/json')) {
      const body = await request.json();
      targetUrl = body.url;
    } else if (contentType.includes('application/x-www-form-urlencoded')) {
      const formData = await request.formData();
      targetUrl = formData.get('url') as string;
    } else {
      // Try JSON first as fallback
      try {
        const body = await request.json();
        targetUrl = body.url;
      } catch (e) {
        return new Response(JSON.stringify({
          error: 'Invalid content type. Expected application/json or application/x-www-form-urlencoded'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    console.log('POST request - received URL:', targetUrl);

    if (!targetUrl || targetUrl.trim() === '') {
      return new Response(JSON.stringify({
        error: 'URL parameter is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Normalize URL
    let normalizedUrl = targetUrl.trim();
    if (!normalizedUrl.match(/^https?:\/\//)) {
      normalizedUrl = 'https://' + normalizedUrl;
    }

    // Validate URL
    let urlObj;
    try {
      urlObj = new URL(normalizedUrl);
    } catch (e) {
      return new Response(JSON.stringify({
        error: 'Invalid URL format. Please provide a valid URL (e.g., https://example.com)'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return new Response(JSON.stringify({
        error: 'Invalid URL protocol. Only HTTP and HTTPS are supported.'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Block localhost and private IPs for security
    if (urlObj.hostname === 'localhost' ||
        urlObj.hostname.startsWith('127.') ||
        urlObj.hostname.startsWith('192.168.') ||
        urlObj.hostname.startsWith('10.') ||
        urlObj.hostname.includes('internal')) {
      return new Response(JSON.stringify({
        error: 'Cannot fetch local or private URLs for security reasons.'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log('Fetching URL:', normalizedUrl);

    // Fetch the target URL
    const response = await fetch(normalizedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ConvertIQ/1.0)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive'
      },
      redirect: 'follow',
      signal: AbortSignal.timeout(15000)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    console.log('Successfully fetched HTML, length:', html.length);

    return new Response(JSON.stringify({
      html,
      url: normalizedUrl,
      status: response.status
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in fetch-url API:', error);

    let errorMessage = 'Failed to fetch URL. Please check the URL and try again.';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        errorMessage = 'Request timeout. The website took too long to respond.';
        statusCode = 408;
      } else if (error.message.includes('fetch') || error.message.includes('ENOTFOUND')) {
        errorMessage = 'Unable to access the website. It may be down or blocking requests.';
        statusCode = 502;
      } else if (error.message.includes('ECONNREFUSED')) {
        errorMessage = 'Connection refused. The website may be temporarily unavailable.';
        statusCode = 503;
      } else if (error.message.includes('certificate') || error.message.includes('SSL')) {
        errorMessage = 'SSL certificate error. The website may have security issues.';
        statusCode = 502;
      }
    }

    return new Response(JSON.stringify({
      error: errorMessage,
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: statusCode,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
