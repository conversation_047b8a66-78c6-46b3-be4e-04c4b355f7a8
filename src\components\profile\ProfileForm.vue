<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';

const user = ref(null);
const displayName = ref('');
const email = ref('');
const loading = ref(true);
const updating = ref(false);
const message = ref({ type: '', text: '' });

onMounted(async () => {
  await loadUserProfile();
});

const loadUserProfile = async () => {
  try {
    loading.value = true;
    
    const { data } = await supabase.auth.getUser();
    if (!data?.user) return;
    
    user.value = data.user;
    email.value = data.user.email;
    
    // Get profile data
    const { data: profileData } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', data.user.id)
      .single();
    
    if (profileData) {
      displayName.value = profileData.display_name || '';
    }
  } catch (error) {
    console.error('Error loading profile:', error);
    message.value = { type: 'error', text: 'Failed to load profile data' };
  } finally {
    loading.value = false;
  }
};

const updateProfile = async () => {
  if (!user.value) return;
  
  try {
    updating.value = true;
    message.value = { type: '', text: '' };
    
    const { error } = await supabase
      .from('profiles')
      .upsert({
        id: user.value.id,
        email: email.value,
        display_name: displayName.value,
        updated_at: new Date().toISOString()
      });
    
    if (error) throw error;
    
    message.value = { type: 'success', text: 'Profile updated successfully!' };
  } catch (error) {
    console.error('Error updating profile:', error);
    message.value = { type: 'error', text: 'Failed to update profile' };
  } finally {
    updating.value = false;
  }
};
</script>

<template>
  <div>
    <h2 class="text-xl font-medium mb-6">Personal Information</h2>
    
    <div v-if="loading" class="animate-pulse space-y-4">
      <div class="h-4 bg-gray-200 rounded w-1/4"></div>
      <div class="h-10 bg-gray-200 rounded"></div>
      <div class="h-4 bg-gray-200 rounded w-1/4 mt-4"></div>
      <div class="h-10 bg-gray-200 rounded"></div>
    </div>
    
    <form v-else @submit.prevent="updateProfile" class="space-y-6">
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label for="displayName" class="block text-sm font-medium text-gray-700 mb-1">
            Display Name
          </label>
          <input
            id="displayName"
            v-model="displayName"
            type="text"
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          >
        </div>
        
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            id="email"
            v-model="email"
            type="email"
            disabled
            class="w-full rounded-md border-gray-300 bg-gray-100 shadow-sm cursor-not-allowed"
          >
          <p class="mt-1 text-xs text-gray-500">Email cannot be changed</p>
        </div>
      </div>
      
      <div v-if="message.text" :class="`p-4 rounded-md ${message.type === 'error' ? 'bg-error-500/10 text-error-500' : 'bg-success-500/10 text-success-500'}`">
        {{ message.text }}
      </div>
      
      <div>
        <button
          type="submit"
          :disabled="updating"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          <svg v-if="updating" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ updating ? 'Saving...' : 'Save Changes' }}
        </button>
      </div>
    </form>
  </div>
</template>