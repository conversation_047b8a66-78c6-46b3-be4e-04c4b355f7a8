---
export const prerender = false;
import Layout from '../../../layouts/Layout.astro';
import Navbar from '../../../components/dashboard/Navbar.vue';
import AnalysisResultsNew from '../../../components/analysis/AnalysisResultsNew.vue';

const { id } = Astro.params;

if (!id) {
  return Astro.redirect('/dashboard');
}
---

<Layout title="Analysis Results - ConvertIQ" isAuthenticated={true}>
  <div class="flex-1 flex flex-col overflow-hidden">
    <Navbar client:load />
    <main class="flex-1 overflow-y-auto bg-gray-50">
      <AnalysisResultsNew client:load analysisId={id} />
    </main>
  </div>
</Layout>