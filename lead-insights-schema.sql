-- Lead Insights Database Schema Updates
-- Execute this in your Supabase SQL Editor

-- 1. Create lead_insights table for storing lead generation insights
CREATE TABLE IF NOT EXISTS public.lead_insights (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    analysis_id UUID NOT NULL REFERENCES public.analyses(id) ON DELETE CASCADE,
    insight_type VARCHAR(50) NOT NULL, -- 'opportunity', 'growth', 'conversion', 'content'
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    impact_level VARCHAR(20) NOT NULL DEFAULT 'Medium', -- 'High', 'Medium', 'Low'
    business_value TEXT,
    questions JSONB DEFAULT '[]'::jsonb, -- Array of qualification questions
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create lead_qualification_questions table
CREATE TABLE IF NOT EXISTS public.lead_qualification_questions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    analysis_id UUID NOT NULL REFERENCES public.analyses(id) ON DELETE CASCADE,
    category VARCHAR(100) NOT NULL, -- 'Business Context', 'Target Audience', etc.
    question TEXT NOT NULL,
    purpose TEXT NOT NULL,
    follow_up_areas JSONB DEFAULT '[]'::jsonb, -- Array of follow-up topics
    priority INTEGER DEFAULT 1, -- 1 = highest priority
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create lead_scoring_factors table for tracking what affects lead quality
CREATE TABLE IF NOT EXISTS public.lead_scoring_factors (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    analysis_id UUID NOT NULL REFERENCES public.analyses(id) ON DELETE CASCADE,
    factor_name VARCHAR(100) NOT NULL,
    factor_type VARCHAR(50) NOT NULL, -- 'performance', 'seo', 'conversion', 'content'
    weight DECIMAL(3,2) DEFAULT 1.0, -- Importance weight (0.0 to 1.0)
    current_score INTEGER, -- Current score for this factor (0-100)
    potential_improvement INTEGER, -- Potential score improvement (0-100)
    business_impact TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Add lead-related columns to existing analyses table
ALTER TABLE public.analyses 
ADD COLUMN IF NOT EXISTS lead_generation_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS lead_quality_indicators JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS conversion_funnel_data JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS target_audience_data JSONB DEFAULT '{}'::jsonb;

-- 5. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_lead_insights_analysis_id ON public.lead_insights(analysis_id);
CREATE INDEX IF NOT EXISTS idx_lead_insights_type ON public.lead_insights(insight_type);
CREATE INDEX IF NOT EXISTS idx_lead_qualification_analysis_id ON public.lead_qualification_questions(analysis_id);
CREATE INDEX IF NOT EXISTS idx_lead_qualification_category ON public.lead_qualification_questions(category);
CREATE INDEX IF NOT EXISTS idx_lead_scoring_analysis_id ON public.lead_scoring_factors(analysis_id);
CREATE INDEX IF NOT EXISTS idx_lead_scoring_type ON public.lead_scoring_factors(factor_type);

-- 6. Create RLS (Row Level Security) policies
ALTER TABLE public.lead_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lead_qualification_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lead_scoring_factors ENABLE ROW LEVEL SECURITY;

-- Lead insights policies
CREATE POLICY "Users can view their own lead insights" ON public.lead_insights
    FOR SELECT USING (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own lead insights" ON public.lead_insights
    FOR INSERT WITH CHECK (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own lead insights" ON public.lead_insights
    FOR UPDATE USING (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own lead insights" ON public.lead_insights
    FOR DELETE USING (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

-- Lead qualification questions policies
CREATE POLICY "Users can view their own lead qualification questions" ON public.lead_qualification_questions
    FOR SELECT USING (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own lead qualification questions" ON public.lead_qualification_questions
    FOR INSERT WITH CHECK (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own lead qualification questions" ON public.lead_qualification_questions
    FOR UPDATE USING (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own lead qualification questions" ON public.lead_qualification_questions
    FOR DELETE USING (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

-- Lead scoring factors policies
CREATE POLICY "Users can view their own lead scoring factors" ON public.lead_scoring_factors
    FOR SELECT USING (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own lead scoring factors" ON public.lead_scoring_factors
    FOR INSERT WITH CHECK (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own lead scoring factors" ON public.lead_scoring_factors
    FOR UPDATE USING (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own lead scoring factors" ON public.lead_scoring_factors
    FOR DELETE USING (
        analysis_id IN (
            SELECT id FROM public.analyses WHERE user_id = auth.uid()
        )
    );

-- 7. Create functions for automatic lead insights generation
CREATE OR REPLACE FUNCTION public.generate_lead_insights(analysis_uuid UUID)
RETURNS VOID AS $$
DECLARE
    analysis_record RECORD;
    performance_score INTEGER;
    seo_score INTEGER;
    conversion_score INTEGER;
BEGIN
    -- Get analysis data
    SELECT * INTO analysis_record FROM public.analyses WHERE id = analysis_uuid;
    
    IF NOT FOUND THEN
        RETURN;
    END IF;
    
    performance_score := COALESCE(analysis_record.performance_score, 0);
    seo_score := COALESCE(analysis_record.seo_score, 0);
    conversion_score := COALESCE(analysis_record.score, 0);
    
    -- Clear existing insights
    DELETE FROM public.lead_insights WHERE analysis_id = analysis_uuid;
    DELETE FROM public.lead_qualification_questions WHERE analysis_id = analysis_uuid;
    DELETE FROM public.lead_scoring_factors WHERE analysis_id = analysis_uuid;
    
    -- Generate performance-based insights
    IF performance_score < 70 THEN
        INSERT INTO public.lead_insights (analysis_id, insight_type, title, description, impact_level, business_value, questions)
        VALUES (
            analysis_uuid,
            'opportunity',
            'Performance Optimization Opportunity',
            'Poor page performance is likely causing visitor drop-off. This represents a significant lead generation opportunity.',
            'High',
            'Improving performance could increase lead capture by 15-25%',
            '["How many visitors do you lose due to slow loading?", "What is your current bounce rate?"]'::jsonb
        );
    END IF;
    
    -- Generate SEO-based insights
    IF seo_score < 80 THEN
        INSERT INTO public.lead_insights (analysis_id, insight_type, title, description, impact_level, business_value, questions)
        VALUES (
            analysis_uuid,
            'growth',
            'SEO Lead Generation Potential',
            'SEO improvements could significantly increase organic traffic and lead volume.',
            'Medium',
            'Better SEO could increase qualified leads by 20-40%',
            '["What is your current organic traffic volume?", "Which keywords drive the most qualified leads?"]'::jsonb
        );
    END IF;
    
    -- Generate conversion insights
    INSERT INTO public.lead_insights (analysis_id, insight_type, title, description, impact_level, business_value, questions)
    VALUES (
        analysis_uuid,
        'conversion',
        'Conversion Rate Optimization',
        'Strategic improvements to messaging and design could significantly boost lead conversion rates.',
        'High',
        'CRO improvements typically increase conversions by 10-30%',
        '["What is your current conversion rate?", "What objections do prospects typically have?"]'::jsonb
    );
    
    -- Generate qualification questions
    INSERT INTO public.lead_qualification_questions (analysis_id, category, question, purpose, follow_up_areas, priority)
    VALUES 
        (analysis_uuid, 'Business Context', 'What is your primary business goal for this landing page?', 'Understanding business objectives helps prioritize optimization efforts', '["Lead generation", "Sales conversion", "Brand awareness", "Product education"]'::jsonb, 1),
        (analysis_uuid, 'Target Audience', 'Who is your ideal customer visiting this page?', 'Audience insights help tailor messaging and design improvements', '["Demographics", "Pain points", "Buying behavior", "Technical expertise level"]'::jsonb, 2),
        (analysis_uuid, 'Conversion Goals', 'What specific action do you want visitors to take on this page?', 'Clear conversion goals help optimize the user journey and CTA placement', '["Primary CTA", "Secondary actions", "Form completion", "Contact preferences"]'::jsonb, 3);
    
    -- Generate scoring factors
    INSERT INTO public.lead_scoring_factors (analysis_id, factor_name, factor_type, weight, current_score, potential_improvement, business_impact)
    VALUES 
        (analysis_uuid, 'Page Performance', 'performance', 0.3, performance_score, GREATEST(0, 90 - performance_score), 'Faster pages convert better and reduce bounce rates'),
        (analysis_uuid, 'SEO Optimization', 'seo', 0.25, seo_score, GREATEST(0, 85 - seo_score), 'Better SEO brings more qualified organic traffic'),
        (analysis_uuid, 'Conversion Design', 'conversion', 0.35, conversion_score, GREATEST(0, 85 - conversion_score), 'Optimized design and messaging improve lead quality'),
        (analysis_uuid, 'Content Quality', 'content', 0.1, 75, 15, 'High-quality content builds trust and improves lead nurturing');
    
    -- Update lead generation score
    UPDATE public.analyses 
    SET lead_generation_score = (performance_score * 0.3 + seo_score * 0.25 + conversion_score * 0.35 + 75 * 0.1)::INTEGER
    WHERE id = analysis_uuid;
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create trigger to automatically generate lead insights when analysis is updated
CREATE OR REPLACE FUNCTION public.trigger_generate_lead_insights()
RETURNS TRIGGER AS $$
BEGIN
    -- Only generate insights if scores have been updated
    IF (NEW.performance_score IS NOT NULL OR NEW.seo_score IS NOT NULL OR NEW.score IS NOT NULL) AND
       (OLD.performance_score IS DISTINCT FROM NEW.performance_score OR 
        OLD.seo_score IS DISTINCT FROM NEW.seo_score OR 
        OLD.score IS DISTINCT FROM NEW.score) THEN
        
        PERFORM public.generate_lead_insights(NEW.id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
DROP TRIGGER IF EXISTS trigger_analysis_lead_insights ON public.analyses;
CREATE TRIGGER trigger_analysis_lead_insights
    AFTER UPDATE ON public.analyses
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_generate_lead_insights();

-- 9. Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.lead_insights TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.lead_qualification_questions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.lead_scoring_factors TO authenticated;
GRANT EXECUTE ON FUNCTION public.generate_lead_insights(UUID) TO authenticated;

-- 10. Create sample data for existing analyses (optional)
-- Uncomment the following lines if you want to generate lead insights for existing analyses
/*
DO $$
DECLARE
    analysis_record RECORD;
BEGIN
    FOR analysis_record IN SELECT id FROM public.analyses WHERE performance_score IS NOT NULL OR seo_score IS NOT NULL OR score IS NOT NULL
    LOOP
        PERFORM public.generate_lead_insights(analysis_record.id);
    END LOOP;
END $$;
*/

-- Verification queries (run these to check if everything was created correctly)
-- SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE '%lead%';
-- SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'analyses' AND column_name LIKE '%lead%';
-- SELECT * FROM public.lead_insights LIMIT 5;
-- SELECT * FROM public.lead_qualification_questions LIMIT 5;
-- SELECT * FROM public.lead_scoring_factors LIMIT 5;
