---
import '../styles/global.css';


interface Props {
  title: string;
  description?: string;
  isAuthenticated?: boolean;
}

const { 
  title, 
  description = "ConvertIQ - AI-powered landing page analysis and optimization",
  isAuthenticated = false
} = Astro.props;
---

<script>
  // Apply theme immediately to prevent flash
  const savedTheme = localStorage.getItem('convertiq-theme');
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const theme = savedTheme || (prefersDark ? 'dark' : 'light');
  
  if (theme === 'dark') {
    document.documentElement.classList.add('dark');
  }
</script>

<!DOCTYPE html>
<html lang="en" class="h-full scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <meta name="description" content={description} />
    <title>{title}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  </head>
  <body class="min-h-full bg-background-primary font-sans text-gray-900 antialiased">
    {isAuthenticated ? (
      <div class="flex h-screen overflow-hidden bg-background-primary">
        <slot />
      </div>
    ) : (
      <div class="min-h-screen bg-background-primary">
        <slot />
      </div>
    )}
  </body>
</html>